module.exports = {
  rootDir: '.',
  roots: ['<rootDir>', '<rootDir>/src'],
  testEnvironment: 'jsdom',
  testMatch: ['**/*.test.ts', '**/*.test.tsx'],
  transform: {
    '^.+\\.(j|t)sx?$': 'babel-jest',
  },
  moduleNameMapper: {
    '\\.(css)$': 'identity-obj-proxy',
    'single-spa-react/parcel': 'single-spa-react/lib/cjs/parcel.cjs',
    '^.+\\.(css|less|scss)$': 'babel-jest',
  },
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  transformIgnorePatterns: ['node_modules/(?!axios)'],
  testTimeout: 10000,
  coveragePathIgnorePatterns: ['/node_modules/', 'src/enums/'],
    collectCoverageFrom: [
    '**/*.{ts,tsx}',
    '!**/node_modules/**',
    '!**/vendor/**',
    '!*.test.*',
    '!**/types/**',
    '!**/__tests__/**',
    '!**/__test__/**',
  ],
};
