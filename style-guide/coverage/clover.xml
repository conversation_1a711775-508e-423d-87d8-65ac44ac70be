<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755845509572" clover="3.2.0">
  <project timestamp="1755845509572" name="All files">
    <metrics statements="621" coveredstatements="80" conditionals="405" coveredconditionals="71" methods="214" coveredmethods="25" elements="1240" coveredelements="176" complexity="0" loc="621" ncloc="621" packages="13" files="46" classes="46"/>
    <package name="src">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="import-png.d.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/import-png.d.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="set-public-path.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/set-public-path.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="30" coveredstatements="29" conditionals="16" coveredconditionals="14" methods="8" coveredmethods="8"/>
      <file name="BreadcrumbHeader.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/BreadcrumbHeader.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="3" coveredconditionals="2" methods="2" coveredmethods="2"/>
        <line num="18" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="19" count="4" type="stmt"/>
        <line num="21" count="12" type="stmt"/>
        <line num="22" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="23" count="12" type="stmt"/>
      </file>
      <file name="ErrorPage.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/ErrorPage.tsx">
        <metrics statements="15" coveredstatements="14" conditionals="8" coveredconditionals="8" methods="4" coveredmethods="4"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="5" type="cond" truecount="3" falsecount="0"/>
        <line num="11" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="5" type="stmt"/>
        <line num="43" count="5" type="stmt"/>
        <line num="44" count="4" type="stmt"/>
        <line num="45" count="4" type="stmt"/>
        <line num="46" count="4" type="stmt"/>
        <line num="47" count="4" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="55" count="5" type="stmt"/>
        <line num="56" count="5" type="stmt"/>
      </file>
      <file name="Icon.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Icon.tsx">
        <metrics statements="8" coveredstatements="8" conditionals="5" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="3" count="1" type="stmt"/>
        <line num="13" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="14" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="15" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="20" count="4" type="stmt"/>
        <line num="22" count="4" type="stmt"/>
      </file>
      <file name="YoutubeEmbed.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/YoutubeEmbed.tsx">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="3" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.DashboardWidget.common">
      <metrics statements="88" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="32" coveredmethods="0"/>
      <file name="BarChart.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/BarChart.tsx">
        <metrics statements="24" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="25" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
      </file>
      <file name="ChartComponents.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/ChartComponents.tsx">
        <metrics statements="43" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
      </file>
      <file name="DashboardGrid.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="DeficiencyCountCard.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DeficiencyCountCard.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="DonutChartAdapter.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DonutChartAdapter.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
      </file>
      <file name="DonutChartCard.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DonutChartCard.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
      </file>
      <file name="Dynamic3DDonutChart.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/Dynamic3DDonutChart.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="EmptyState.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/EmptyState.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DashboardWidget.common.DashboardGrid">
      <metrics statements="32" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="Dashboard.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/Dashboard.tsx">
        <metrics statements="32" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="53" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DashboardWidget.common.DashboardGrid.StatCard">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="StatCard.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/StatCard/StatCard.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DashboardWidget.common.DashboardGrid.StatusIndicatorCard">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="StatusIndicatorCard.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/StatusIndicatorCard/StatusIndicatorCard.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DashboardWidget.hooks">
      <metrics statements="135" coveredstatements="0" conditionals="60" coveredconditionals="0" methods="39" coveredmethods="0"/>
      <file name="useD3Chart.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useD3Chart.ts">
        <metrics statements="28" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
      </file>
      <file name="useDropdown.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useDropdown.ts">
        <metrics statements="15" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
      </file>
      <file name="useHighchartsDonut.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useHighchartsDonut.ts">
        <metrics statements="25" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
      </file>
      <file name="useInfiniteQuery.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useInfiniteQuery.ts">
        <metrics statements="46" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
      </file>
      <file name="useInfiniteScroll.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useInfiniteScroll.ts">
        <metrics statements="21" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DashboardWidget.iCard">
      <metrics statements="238" coveredstatements="0" conditionals="204" coveredconditionals="0" methods="94" coveredmethods="0"/>
      <file name="CardDropdown.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdown.tsx">
        <metrics statements="37" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
      </file>
      <file name="CardDropdownMenu.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdownMenu.tsx">
        <metrics statements="14" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="49" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
      </file>
      <file name="CardDropdownSelectors.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdownSelectors.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
      <file name="CardGrid.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardGrid.tsx">
        <metrics statements="19" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
      </file>
      <file name="CardModule.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardModule.tsx">
        <metrics statements="46" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
      </file>
      <file name="CardModuleHeader.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardModuleHeader.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="17" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
      </file>
      <file name="CardSelectGroup.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardSelectGroup.tsx">
        <metrics statements="13" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
      </file>
      <file name="CardTable.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardTable.tsx">
        <metrics statements="20" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
      </file>
      <file name="CardTabs.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardTabs.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
      <file name="InfiniteScrollTable.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/InfiniteScrollTable.tsx">
        <metrics statements="52" coveredstatements="0" conditionals="54" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="253" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
      </file>
      <file name="ModuleModal.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/ModuleModal.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
      </file>
      <file name="Spinner.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/Spinner.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="svgIcons.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/svgIcons.tsx">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DashboardWidget.utils">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="textConstants.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/utils/textConstants.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DropdownSearch">
      <metrics statements="18" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="index.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DropdownSearch/index.tsx">
        <metrics statements="18" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.SendEmailModal">
      <metrics statements="26" coveredstatements="6" conditionals="10" coveredconditionals="1" methods="8" coveredmethods="3"/>
      <file name="index.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/SendEmailModal/index.tsx">
        <metrics statements="20" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="25" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
      </file>
      <file name="useYupValidationResolver.ts" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/SendEmailModal/useYupValidationResolver.ts">
        <metrics statements="6" coveredstatements="6" conditionals="2" coveredconditionals="1" methods="3" coveredmethods="3"/>
        <line num="4" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.components.StatCard">
      <metrics statements="2" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="StatCard.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/StatCard/StatCard.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.Stepper">
      <metrics statements="45" coveredstatements="45" conditionals="60" coveredconditionals="56" methods="14" coveredmethods="14"/>
      <file name="BottomButton.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/BottomButton.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="8" coveredconditionals="8" methods="2" coveredmethods="2"/>
        <line num="30" count="2" type="stmt"/>
        <line num="31" count="48" type="stmt"/>
        <line num="36" count="93" type="stmt"/>
      </file>
      <file name="Loader.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/Loader.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="19" count="2" type="stmt"/>
        <line num="20" count="15" type="stmt"/>
        <line num="22" count="15" type="stmt"/>
      </file>
      <file name="StepperCard.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/StepperCard.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="4" coveredconditionals="3" methods="3" coveredmethods="3"/>
        <line num="13" count="1" type="stmt"/>
        <line num="18" count="38" type="stmt"/>
        <line num="21" count="107" type="stmt"/>
        <line num="22" count="107" type="stmt"/>
        <line num="23" count="107" type="stmt"/>
        <line num="25" count="107" type="stmt"/>
        <line num="29" count="1" type="cond" truecount="1" falsecount="1"/>
      </file>
      <file name="index.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/index.tsx">
        <metrics statements="28" coveredstatements="28" conditionals="48" coveredconditionals="45" methods="6" coveredmethods="6"/>
        <line num="79" count="1" type="stmt"/>
        <line num="100" count="43" type="cond" truecount="2" falsecount="0"/>
        <line num="102" count="43" type="stmt"/>
        <line num="104" count="43" type="stmt"/>
        <line num="107" count="43" type="stmt"/>
        <line num="108" count="29" type="cond" truecount="1" falsecount="1"/>
        <line num="109" count="29" type="stmt"/>
        <line num="116" count="43" type="stmt"/>
        <line num="117" count="9" type="stmt"/>
        <line num="118" count="9" type="stmt"/>
        <line num="119" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="128" count="43" type="stmt"/>
        <line num="129" count="12" type="stmt"/>
        <line num="130" count="12" type="stmt"/>
        <line num="131" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="132" count="4" type="stmt"/>
        <line num="133" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="138" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="139" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="140" count="8" type="stmt"/>
        <line num="141" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="142" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="144" count="10" type="stmt"/>
        <line num="147" count="43" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="219" count="12" type="stmt"/>
      </file>
      <file name="svgIcons.tsx" path="/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/svgIcons.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="27" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
