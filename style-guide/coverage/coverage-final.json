{"/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/import-png.d.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/import-png.d.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/set-public-path.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/set-public-path.ts", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/BreadcrumbHeader.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/BreadcrumbHeader.tsx", "statementMap": {"0": {"start": {"line": 18, "column": 25}, "end": {"line": 37, "column": 1}}, "1": {"start": {"line": 19, "column": 2}, "end": {"line": 36, "column": 15}}, "2": {"start": {"line": 21, "column": 27}, "end": {"line": 21, "column": 52}}, "3": {"start": {"line": 22, "column": 24}, "end": {"line": 22, "column": 85}}, "4": {"start": {"line": 23, "column": 6}, "end": {"line": 34, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 25}, "end": {"line": 18, "column": 26}}, "loc": {"start": {"line": 19, "column": 2}, "end": {"line": 36, "column": 15}}, "line": 19}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 15}, "end": {"line": 20, "column": 16}}, "loc": {"start": {"line": 20, "column": 25}, "end": {"line": 35, "column": 5}}, "line": 20}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 38}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 36}, "end": {"line": 18, "column": 38}}], "line": 18}, "1": {"loc": {"start": {"line": 22, "column": 24}, "end": {"line": 22, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 39}, "end": {"line": 22, "column": 56}}, {"start": {"line": 22, "column": 59}, "end": {"line": 22, "column": 85}}], "line": 22}}, "s": {"0": 1, "1": 4, "2": 12, "3": 12, "4": 12}, "f": {"0": 4, "1": 12}, "b": {"0": [0], "1": [4, 8]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "98ea411645f83f85b0c4d5f5b787a0e8a151a830"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/ErrorPage.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/ErrorPage.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 19}, "end": {"line": 33, "column": 1}}, "1": {"start": {"line": 9, "column": 2}, "end": {"line": 32, "column": 3}}, "2": {"start": {"line": 11, "column": 6}, "end": {"line": 16, "column": 7}}, "3": {"start": {"line": 18, "column": 6}, "end": {"line": 25, "column": 7}}, "4": {"start": {"line": 27, "column": 6}, "end": {"line": 31, "column": 7}}, "5": {"start": {"line": 40, "column": 18}, "end": {"line": 77, "column": 1}}, "6": {"start": {"line": 41, "column": 40}, "end": {"line": 41, "column": 54}}, "7": {"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 9}}, "8": {"start": {"line": 44, "column": 4}, "end": {"line": 51, "column": 9}}, "9": {"start": {"line": 45, "column": 6}, "end": {"line": 50, "column": 7}}, "10": {"start": {"line": 46, "column": 21}, "end": {"line": 46, "column": 45}}, "11": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 64}}, "12": {"start": {"line": 49, "column": 8}, "end": {"line": 49, "column": 69}}, "13": {"start": {"line": 55, "column": 42}, "end": {"line": 55, "column": 63}}, "14": {"start": {"line": 56, "column": 2}, "end": {"line": 76, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 19}, "end": {"line": 8, "column": 20}}, "loc": {"start": {"line": 8, "column": 47}, "end": {"line": 33, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 40, "column": 18}, "end": {"line": 40, "column": 19}}, "loc": {"start": {"line": 40, "column": 59}, "end": {"line": 77, "column": 1}}, "line": 40}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 12}, "end": {"line": 43, "column": 13}}, "loc": {"start": {"line": 43, "column": 18}, "end": {"line": 52, "column": 3}}, "line": 43}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 44, "column": 5}, "end": {"line": 44, "column": 6}}, "loc": {"start": {"line": 44, "column": 17}, "end": {"line": 51, "column": 5}}, "line": 44}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 32, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 10, "column": 4}, "end": {"line": 16, "column": 7}}, {"start": {"line": 17, "column": 4}, "end": {"line": 25, "column": 7}}, {"start": {"line": 26, "column": 4}, "end": {"line": 31, "column": 7}}], "line": 9}, "1": {"loc": {"start": {"line": 63, "column": 40}, "end": {"line": 63, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 40}, "end": {"line": 63, "column": 53}}, {"start": {"line": 63, "column": 57}, "end": {"line": 63, "column": 64}}], "line": 63}, "2": {"loc": {"start": {"line": 64, "column": 11}, "end": {"line": 68, "column": 18}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 11}, "end": {"line": 64, "column": 23}}, {"start": {"line": 64, "column": 27}, "end": {"line": 64, "column": 44}}, {"start": {"line": 65, "column": 12}, "end": {"line": 68, "column": 18}}], "line": 64}}, "s": {"0": 1, "1": 5, "2": 2, "3": 2, "4": 1, "5": 1, "6": 5, "7": 5, "8": 4, "9": 4, "10": 4, "11": 4, "12": 0, "13": 5, "14": 5}, "f": {"0": 5, "1": 5, "2": 4, "3": 4}, "b": {"0": [2, 2, 1], "1": [5, 4], "2": [5, 1, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "be2ff9b51abbda4bc8977524004666dd7a9e9060"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Icon.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Icon.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 16}, "end": {"line": 3, "column": 44}}, "1": {"start": {"line": 13, "column": 13}, "end": {"line": 27, "column": 1}}, "2": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 31}}, "3": {"start": {"line": 15, "column": 2}, "end": {"line": 18, "column": 3}}, "4": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 28}}, "5": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 27}}, "6": {"start": {"line": 20, "column": 24}, "end": {"line": 20, "column": 76}}, "7": {"start": {"line": 22, "column": 2}, "end": {"line": 26, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 14}}, "loc": {"start": {"line": 13, "column": 80}, "end": {"line": 27, "column": 1}}, "line": 13}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 33}, "end": {"line": 13, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 41}, "end": {"line": 13, "column": 43}}], "line": 13}, "1": {"loc": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 25}}, {"start": {"line": 14, "column": 29}, "end": {"line": 14, "column": 31}}], "line": 14}, "2": {"loc": {"start": {"line": 15, "column": 2}, "end": {"line": 18, "column": 3}}, "type": "if", "locations": [{"start": {"line": 15, "column": 2}, "end": {"line": 18, "column": 3}}, {"start": {}, "end": {}}], "line": 15}}, "s": {"0": 1, "1": 1, "2": 4, "3": 4, "4": 1, "5": 1, "6": 4, "7": 4}, "f": {"0": 4}, "b": {"0": [3], "1": [4, 0], "2": [1, 3]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "bb4f6972796ad616c9a2c45ade70e01011910b2b"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/YoutubeEmbed.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/YoutubeEmbed.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 21}, "end": {"line": 20, "column": 1}}, "1": {"start": {"line": 9, "column": 2}, "end": {"line": 19, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": 22}}, "loc": {"start": {"line": 9, "column": 2}, "end": {"line": 19, "column": 8}}, "line": 9}}, "branchMap": {}, "s": {"0": 1, "1": 3}, "f": {"0": 3}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a755dbb1d99b729d8faa2bd00e7dfc5eed1e3523"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/index.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/BarChart.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/BarChart.tsx", "statementMap": {"0": {"start": {"line": 25, "column": 15}, "end": {"line": 25, "column": 60}}, "1": {"start": {"line": 37, "column": 18}, "end": {"line": 37, "column": 30}}, "2": {"start": {"line": 38, "column": 28}, "end": {"line": 38, "column": 56}}, "3": {"start": {"line": 39, "column": 46}, "end": {"line": 39, "column": 57}}, "4": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 66}}, "5": {"start": {"line": 42, "column": 32}, "end": {"line": 47, "column": 4}}, "6": {"start": {"line": 57, "column": 6}, "end": {"line": 65, "column": 4}}, "7": {"start": {"line": 67, "column": 26}, "end": {"line": 84, "column": 3}}, "8": {"start": {"line": 68, "column": 20}, "end": {"line": 77, "column": 13}}, "9": {"start": {"line": 78, "column": 4}, "end": {"line": 83, "column": 7}}, "10": {"start": {"line": 86, "column": 26}, "end": {"line": 94, "column": 3}}, "11": {"start": {"line": 87, "column": 4}, "end": {"line": 93, "column": 5}}, "12": {"start": {"line": 88, "column": 6}, "end": {"line": 92, "column": 10}}, "13": {"start": {"line": 88, "column": 28}, "end": {"line": 92, "column": 7}}, "14": {"start": {"line": 96, "column": 25}, "end": {"line": 98, "column": 3}}, "15": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 56}}, "16": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 53}}, "17": {"start": {"line": 100, "column": 22}, "end": {"line": 104, "column": 3}}, "18": {"start": {"line": 101, "column": 23}, "end": {"line": 101, "column": 68}}, "19": {"start": {"line": 102, "column": 23}, "end": {"line": 102, "column": 67}}, "20": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 56}}, "21": {"start": {"line": 106, "column": 2}, "end": {"line": 180, "column": 4}}, "22": {"start": {"line": 122, "column": 16}, "end": {"line": 154, "column": 20}}, "23": {"start": {"line": 127, "column": 20}, "end": {"line": 152, "column": 24}}, "24": {"start": {"line": 131, "column": 37}, "end": {"line": 131, "column": 81}}, "25": {"start": {"line": 132, "column": 42}, "end": {"line": 132, "column": 69}}}, "fnMap": {"0": {"name": "<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 27, "column": 24}, "end": {"line": 27, "column": 32}}, "loc": {"start": {"line": 36, "column": 18}, "end": {"line": 181, "column": 1}}, "line": 36}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 67, "column": 26}, "end": {"line": 67, "column": 27}}, "loc": {"start": {"line": 67, "column": 69}, "end": {"line": 84, "column": 3}}, "line": 67}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 86, "column": 26}, "end": {"line": 86, "column": 27}}, "loc": {"start": {"line": 86, "column": 55}, "end": {"line": 94, "column": 3}}, "line": 86}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 88, "column": 17}, "end": {"line": 88, "column": 18}}, "loc": {"start": {"line": 88, "column": 28}, "end": {"line": 92, "column": 7}}, "line": 88}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 96, "column": 25}, "end": {"line": 96, "column": 26}}, "loc": {"start": {"line": 96, "column": 31}, "end": {"line": 98, "column": 3}}, "line": 96}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 97, "column": 15}, "end": {"line": 97, "column": 16}}, "loc": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 53}}, "line": 97}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 100, "column": 22}, "end": {"line": 100, "column": 23}}, "loc": {"start": {"line": 100, "column": 65}, "end": {"line": 104, "column": 3}}, "line": 100}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 121, "column": 34}, "end": {"line": 121, "column": 35}}, "loc": {"start": {"line": 122, "column": 16}, "end": {"line": 154, "column": 20}}, "line": 122}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 126, "column": 36}, "end": {"line": 126, "column": 37}}, "loc": {"start": {"line": 127, "column": 20}, "end": {"line": 152, "column": 24}}, "line": 127}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 131, "column": 31}, "end": {"line": 131, "column": 32}}, "loc": {"start": {"line": 131, "column": 37}, "end": {"line": 131, "column": 81}}, "line": 131}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 132, "column": 35}, "end": {"line": 132, "column": 36}}, "loc": {"start": {"line": 132, "column": 42}, "end": {"line": 132, "column": 69}}, "line": 132}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 29, "column": 10}, "end": {"line": 29, "column": 13}}], "line": 29}, "1": {"loc": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 30, "column": 17}, "end": {"line": 30, "column": 19}}], "line": 30}, "2": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 33, "column": 16}, "end": {"line": 33, "column": 24}}], "line": 33}, "3": {"loc": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 40, "column": 31}, "end": {"line": 40, "column": 34}}, {"start": {"line": 40, "column": 37}, "end": {"line": 40, "column": 66}}], "line": 40}, "4": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 93, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 93, "column": 5}}, {"start": {}, "end": {}}], "line": 87}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/ChartComponents.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/ChartComponents.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 79}, "end": {"line": 88, "column": 1}}, "1": {"start": {"line": 19, "column": 19}, "end": {"line": 19, "column": 44}}, "2": {"start": {"line": 20, "column": 19}, "end": {"line": 20, "column": 44}}, "3": {"start": {"line": 22, "column": 2}, "end": {"line": 42, "column": 53}}, "4": {"start": {"line": 23, "column": 4}, "end": {"line": 34, "column": 5}}, "5": {"start": {"line": 25, "column": 20}, "end": {"line": 29, "column": 18}}, "6": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 37}}, "7": {"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": 35}}, "8": {"start": {"line": 30, "column": 6}, "end": {"line": 33, "column": 38}}, "9": {"start": {"line": 35, "column": 4}, "end": {"line": 41, "column": 5}}, "10": {"start": {"line": 36, "column": 24}, "end": {"line": 39, "column": 29}}, "11": {"start": {"line": 39, "column": 26}, "end": {"line": 39, "column": 28}}, "12": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 97}}, "13": {"start": {"line": 45, "column": 4}, "end": {"line": 75, "column": 7}}, "14": {"start": {"line": 46, "column": 19}, "end": {"line": 46, "column": 34}}, "15": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 54}}, "16": {"start": {"line": 49, "column": 27}, "end": {"line": 49, "column": 29}}, "17": {"start": {"line": 50, "column": 23}, "end": {"line": 50, "column": 24}}, "18": {"start": {"line": 51, "column": 25}, "end": {"line": 51, "column": 28}}, "19": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 30}}, "20": {"start": {"line": 53, "column": 17}, "end": {"line": 53, "column": 49}}, "21": {"start": {"line": 54, "column": 18}, "end": {"line": 59, "column": 30}}, "22": {"start": {"line": 60, "column": 6}, "end": {"line": 74, "column": 7}}, "23": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 24}}, "24": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 35}}, "25": {"start": {"line": 63, "column": 8}, "end": {"line": 73, "column": 9}}, "26": {"start": {"line": 64, "column": 10}, "end": {"line": 64, "column": 21}}, "27": {"start": {"line": 65, "column": 10}, "end": {"line": 65, "column": 37}}, "28": {"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 24}}, "29": {"start": {"line": 67, "column": 10}, "end": {"line": 72, "column": 24}}, "30": {"start": {"line": 78, "column": 2}, "end": {"line": 87, "column": 4}}, "31": {"start": {"line": 97, "column": 55}, "end": {"line": 117, "column": 1}}, "32": {"start": {"line": 98, "column": 19}, "end": {"line": 98, "column": 44}}, "33": {"start": {"line": 100, "column": 2}, "end": {"line": 108, "column": 15}}, "34": {"start": {"line": 101, "column": 4}, "end": {"line": 107, "column": 5}}, "35": {"start": {"line": 102, "column": 20}, "end": {"line": 102, "column": 41}}, "36": {"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 18}}, "37": {"start": {"line": 110, "column": 2}, "end": {"line": 116, "column": 4}}, "38": {"start": {"line": 125, "column": 57}, "end": {"line": 137, "column": 1}}, "39": {"start": {"line": 126, "column": 2}, "end": {"line": 136, "column": 4}}, "40": {"start": {"line": 144, "column": 55}, "end": {"line": 153, "column": 1}}, "41": {"start": {"line": 145, "column": 2}, "end": {"line": 152, "column": 8}}, "42": {"start": {"line": 147, "column": 6}, "end": {"line": 150, "column": 12}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 79}, "end": {"line": 12, "column": 80}}, "loc": {"start": {"line": 18, "column": 6}, "end": {"line": 88, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 12}, "end": {"line": 22, "column": 13}}, "loc": {"start": {"line": 22, "column": 18}, "end": {"line": 42, "column": 3}}, "line": 22}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 25, "column": 51}, "end": {"line": 25, "column": 52}}, "loc": {"start": {"line": 25, "column": 58}, "end": {"line": 28, "column": 7}}, "line": 25}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 39, "column": 20}, "end": {"line": 39, "column": 21}}, "loc": {"start": {"line": 39, "column": 26}, "end": {"line": 39, "column": 28}}, "line": 39}, "4": {"name": "wrap", "decl": {"start": {"line": 44, "column": 11}, "end": {"line": 44, "column": 15}}, "loc": {"start": {"line": 44, "column": 71}, "end": {"line": 76, "column": 3}}, "line": 44}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 45, "column": 14}, "end": {"line": 45, "column": 15}}, "loc": {"start": {"line": 45, "column": 26}, "end": {"line": 75, "column": 5}}, "line": 45}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 97, "column": 55}, "end": {"line": 97, "column": 56}}, "loc": {"start": {"line": 97, "column": 94}, "end": {"line": 117, "column": 1}}, "line": 97}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 13}}, "loc": {"start": {"line": 100, "column": 18}, "end": {"line": 108, "column": 3}}, "line": 100}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 125, "column": 57}, "end": {"line": 125, "column": 58}}, "loc": {"start": {"line": 125, "column": 95}, "end": {"line": 137, "column": 1}}, "line": 125}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 144, "column": 55}, "end": {"line": 144, "column": 56}}, "loc": {"start": {"line": 145, "column": 2}, "end": {"line": 152, "column": 8}}, "line": 145}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 146, "column": 22}, "end": {"line": 146, "column": 23}}, "loc": {"start": {"line": 147, "column": 6}, "end": {"line": 150, "column": 12}}, "line": 147}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 34, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 34, "column": 5}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 41, "column": 5}}, {"start": {}, "end": {}}], "line": 35}, "2": {"loc": {"start": {"line": 53, "column": 17}, "end": {"line": 53, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 17}, "end": {"line": 53, "column": 44}}, {"start": {"line": 53, "column": 48}, "end": {"line": 53, "column": 49}}], "line": 53}, "3": {"loc": {"start": {"line": 63, "column": 8}, "end": {"line": 73, "column": 9}}, "type": "if", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 73, "column": 9}}, {"start": {}, "end": {}}], "line": 63}, "4": {"loc": {"start": {"line": 101, "column": 4}, "end": {"line": 107, "column": 5}}, "type": "if", "locations": [{"start": {"line": 101, "column": 4}, "end": {"line": 107, "column": 5}}, {"start": {}, "end": {}}], "line": 101}, "5": {"loc": {"start": {"line": 130, "column": 17}, "end": {"line": 130, "column": 34}}, "type": "cond-expr", "locations": [{"start": {"line": 130, "column": 29}, "end": {"line": 130, "column": 30}}, {"start": {"line": 130, "column": 33}, "end": {"line": 130, "column": 34}}], "line": 130}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 59}, "end": {"line": 46, "column": 1}}, "1": {"start": {"line": 12, "column": 51}, "end": {"line": 12, "column": 60}}, "2": {"start": {"line": 14, "column": 22}, "end": {"line": 17, "column": 3}}, "3": {"start": {"line": 19, "column": 23}, "end": {"line": 22, "column": 3}}, "4": {"start": {"line": 24, "column": 2}, "end": {"line": 45, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 59}, "end": {"line": 11, "column": 60}}, "loc": {"start": {"line": 11, "column": 78}, "end": {"line": 46, "column": 1}}, "line": 11}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DeficiencyCountCard.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DeficiencyCountCard.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 71}, "end": {"line": 23, "column": 1}}, "1": {"start": {"line": 13, "column": 2}, "end": {"line": 22, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 71}, "end": {"line": 9, "column": 72}}, "loc": {"start": {"line": 12, "column": 6}, "end": {"line": 23, "column": 1}}, "line": 12}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DonutChartAdapter.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DonutChartAdapter.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 33}, "end": {"line": 38, "column": 1}}, "1": {"start": {"line": 12, "column": 20}, "end": {"line": 22, "column": 40}}, "2": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 34}}, "3": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 34}}, "4": {"start": {"line": 14, "column": 4}, "end": {"line": 21, "column": 8}}, "5": {"start": {"line": 14, "column": 49}, "end": {"line": 21, "column": 5}}, "6": {"start": {"line": 18, "column": 56}, "end": {"line": 18, "column": 69}}, "7": {"start": {"line": 25, "column": 2}, "end": {"line": 37, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 33}, "end": {"line": 9, "column": 34}}, "loc": {"start": {"line": 9, "column": 64}, "end": {"line": 38, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 29}}, "loc": {"start": {"line": 12, "column": 34}, "end": {"line": 22, "column": 3}}, "line": 12}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 29}, "end": {"line": 14, "column": 30}}, "loc": {"start": {"line": 14, "column": 49}, "end": {"line": 21, "column": 5}}, "line": 14}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 18, "column": 38}, "end": {"line": 18, "column": 39}}, "loc": {"start": {"line": 18, "column": 56}, "end": {"line": 18, "column": 69}}, "line": 18}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 34}}, "type": "if", "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 34}}, {"start": {}, "end": {}}], "line": 13}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DonutChartCard.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DonutChartCard.tsx", "statementMap": {"0": {"start": {"line": 18, "column": 61}, "end": {"line": 42, "column": 1}}, "1": {"start": {"line": 23, "column": 24}, "end": {"line": 28, "column": 5}}, "2": {"start": {"line": 23, "column": 44}, "end": {"line": 28, "column": 3}}, "3": {"start": {"line": 30, "column": 2}, "end": {"line": 41, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 61}, "end": {"line": 18, "column": 62}}, "loc": {"start": {"line": 22, "column": 6}, "end": {"line": 42, "column": 1}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 33}, "end": {"line": 23, "column": 34}}, "loc": {"start": {"line": 23, "column": 44}, "end": {"line": 28, "column": 3}}, "line": 23}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/Dynamic3DDonutChart.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/Dynamic3DDonutChart.tsx", "statementMap": {"0": {"start": {"line": 21, "column": 55}, "end": {"line": 34, "column": 1}}, "1": {"start": {"line": 22, "column": 36}, "end": {"line": 22, "column": 70}}, "2": {"start": {"line": 24, "column": 2}, "end": {"line": 33, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 55}, "end": {"line": 21, "column": 56}}, "loc": {"start": {"line": 21, "column": 75}, "end": {"line": 34, "column": 1}}, "line": 21}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/EmptyState.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/EmptyState.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 46}, "end": {"line": 31, "column": 1}}, "1": {"start": {"line": 9, "column": 2}, "end": {"line": 30, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 46}, "end": {"line": 8, "column": 47}}, "loc": {"start": {"line": 8, "column": 60}, "end": {"line": 31, "column": 1}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/Dashboard.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/Dashboard.tsx", "statementMap": {"0": {"start": {"line": 22, "column": 78}, "end": {"line": 39, "column": 1}}, "1": {"start": {"line": 26, "column": 2}, "end": {"line": 38, "column": 8}}, "2": {"start": {"line": 30, "column": 8}, "end": {"line": 35, "column": 10}}, "3": {"start": {"line": 42, "column": 70}, "end": {"line": 45, "column": 1}}, "4": {"start": {"line": 47, "column": 44}, "end": {"line": 134, "column": 1}}, "5": {"start": {"line": 48, "column": 35}, "end": {"line": 48, "column": 64}}, "6": {"start": {"line": 51, "column": 42}, "end": {"line": 114, "column": 28}}, "7": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, "8": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 53}}, "9": {"start": {"line": 57, "column": 27}, "end": {"line": 57, "column": 41}}, "10": {"start": {"line": 58, "column": 26}, "end": {"line": 58, "column": 27}}, "11": {"start": {"line": 62, "column": 76}, "end": {"line": 62, "column": 78}}, "12": {"start": {"line": 65, "column": 4}, "end": {"line": 83, "column": 7}}, "13": {"start": {"line": 66, "column": 24}, "end": {"line": 66, "column": 69}}, "14": {"start": {"line": 67, "column": 21}, "end": {"line": 67, "column": 66}}, "15": {"start": {"line": 69, "column": 6}, "end": {"line": 71, "column": 7}}, "16": {"start": {"line": 70, "column": 8}, "end": {"line": 70, "column": 26}}, "17": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "18": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 37}}, "19": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "20": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 44}}, "21": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 40}}, "22": {"start": {"line": 86, "column": 30}, "end": {"line": 89, "column": 5}}, "23": {"start": {"line": 91, "column": 56}, "end": {"line": 96, "column": 5}}, "24": {"start": {"line": 99, "column": 33}, "end": {"line": 108, "column": 9}}, "25": {"start": {"line": 100, "column": 27}, "end": {"line": 100, "column": 49}}, "26": {"start": {"line": 101, "column": 35}, "end": {"line": 108, "column": 7}}, "27": {"start": {"line": 103, "column": 69}, "end": {"line": 107, "column": 9}}, "28": {"start": {"line": 110, "column": 4}, "end": {"line": 113, "column": 6}}, "29": {"start": {"line": 116, "column": 2}, "end": {"line": 133, "column": 4}}, "30": {"start": {"line": 120, "column": 10}, "end": {"line": 120, "column": 72}}, "31": {"start": {"line": 126, "column": 8}, "end": {"line": 130, "column": 10}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 22, "column": 78}, "end": {"line": 22, "column": 79}}, "loc": {"start": {"line": 26, "column": 2}, "end": {"line": 38, "column": 8}}, "line": 26}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 20}, "end": {"line": 29, "column": 21}}, "loc": {"start": {"line": 30, "column": 8}, "end": {"line": 35, "column": 10}}, "line": 30}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 44}, "end": {"line": 47, "column": 45}}, "loc": {"start": {"line": 47, "column": 55}, "end": {"line": 134, "column": 1}}, "line": 47}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 51, "column": 50}, "end": {"line": 51, "column": 51}}, "loc": {"start": {"line": 51, "column": 56}, "end": {"line": 114, "column": 3}}, "line": 51}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 65, "column": 20}, "end": {"line": 65, "column": 21}}, "loc": {"start": {"line": 65, "column": 32}, "end": {"line": 83, "column": 5}}, "line": 65}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 15}}, "loc": {"start": {"line": 100, "column": 27}, "end": {"line": 100, "column": 49}}, "line": 100}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 101, "column": 11}, "end": {"line": 101, "column": 12}}, "loc": {"start": {"line": 101, "column": 35}, "end": {"line": 108, "column": 7}}, "line": 101}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 103, "column": 47}, "end": {"line": 103, "column": 48}}, "loc": {"start": {"line": 103, "column": 69}, "end": {"line": 107, "column": 9}}, "line": 103}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 119, "column": 22}, "end": {"line": 119, "column": 23}}, "loc": {"start": {"line": 120, "column": 10}, "end": {"line": 120, "column": 72}}, "line": 120}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 125, "column": 29}, "end": {"line": 125, "column": 30}}, "loc": {"start": {"line": 126, "column": 8}, "end": {"line": 130, "column": 10}}, "line": 126}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, {"start": {}, "end": {}}], "line": 52}, "1": {"loc": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 16}}, {"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 40}}], "line": 52}, "2": {"loc": {"start": {"line": 66, "column": 24}, "end": {"line": 66, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 25}, "end": {"line": 66, "column": 55}}, {"start": {"line": 66, "column": 60}, "end": {"line": 66, "column": 69}}], "line": 66}, "3": {"loc": {"start": {"line": 67, "column": 21}, "end": {"line": 67, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 22}, "end": {"line": 67, "column": 52}}, {"start": {"line": 67, "column": 57}, "end": {"line": 67, "column": 66}}], "line": 67}, "4": {"loc": {"start": {"line": 69, "column": 6}, "end": {"line": 71, "column": 7}}, "type": "if", "locations": [{"start": {"line": 69, "column": 6}, "end": {"line": 71, "column": 7}}, {"start": {}, "end": {}}], "line": 69}, "5": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, {"start": {}, "end": {}}], "line": 74}, "6": {"loc": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "type": "if", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, {"start": {}, "end": {}}], "line": 78}, "7": {"loc": {"start": {"line": 106, "column": 17}, "end": {"line": 106, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 17}, "end": {"line": 106, "column": 41}}, {"start": {"line": 106, "column": 45}, "end": {"line": 106, "column": 54}}], "line": 106}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/StatCard/StatCard.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/StatCard/StatCard.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 42}, "end": {"line": 16, "column": 1}}, "1": {"start": {"line": 10, "column": 2}, "end": {"line": 15, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 42}, "end": {"line": 9, "column": 43}}, "loc": {"start": {"line": 9, "column": 64}, "end": {"line": 16, "column": 1}}, "line": 9}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/StatusIndicatorCard/StatusIndicatorCard.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/common/DashboardGrid/StatusIndicatorCard/StatusIndicatorCard.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 64}, "end": {"line": 22, "column": 1}}, "1": {"start": {"line": 12, "column": 22}, "end": {"line": 14, "column": 3}}, "2": {"start": {"line": 16, "column": 2}, "end": {"line": 21, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 64}, "end": {"line": 10, "column": 65}}, "loc": {"start": {"line": 10, "column": 93}, "end": {"line": 22, "column": 1}}, "line": 10}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useD3Chart.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useD3Chart.ts", "statementMap": {"0": {"start": {"line": 14, "column": 26}, "end": {"line": 111, "column": 1}}, "1": {"start": {"line": 23, "column": 22}, "end": {"line": 23, "column": 98}}, "2": {"start": {"line": 23, "column": 36}, "end": {"line": 23, "column": 65}}, "3": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 62}}, "4": {"start": {"line": 26, "column": 15}, "end": {"line": 35, "column": 1}}, "5": {"start": {"line": 28, "column": 4}, "end": {"line": 33, "column": 18}}, "6": {"start": {"line": 37, "column": 17}, "end": {"line": 45, "column": 3}}, "7": {"start": {"line": 39, "column": 6}, "end": {"line": 43, "column": 21}}, "8": {"start": {"line": 41, "column": 38}, "end": {"line": 41, "column": 50}}, "9": {"start": {"line": 47, "column": 24}, "end": {"line": 50, "column": 3}}, "10": {"start": {"line": 48, "column": 10}, "end": {"line": 48, "column": 83}}, "11": {"start": {"line": 52, "column": 25}, "end": {"line": 58, "column": 33}}, "12": {"start": {"line": 53, "column": 25}, "end": {"line": 53, "column": 79}}, "13": {"start": {"line": 53, "column": 42}, "end": {"line": 53, "column": 78}}, "14": {"start": {"line": 54, "column": 4}, "end": {"line": 57, "column": 44}}, "15": {"start": {"line": 60, "column": 25}, "end": {"line": 100, "column": 3}}, "16": {"start": {"line": 62, "column": 6}, "end": {"line": 98, "column": 8}}, "17": {"start": {"line": 63, "column": 22}, "end": {"line": 63, "column": 23}}, "18": {"start": {"line": 64, "column": 25}, "end": {"line": 95, "column": 11}}, "19": {"start": {"line": 66, "column": 26}, "end": {"line": 66, "column": 54}}, "20": {"start": {"line": 69, "column": 12}, "end": {"line": 71, "column": 13}}, "21": {"start": {"line": 70, "column": 14}, "end": {"line": 70, "column": 26}}, "22": {"start": {"line": 73, "column": 33}, "end": {"line": 73, "column": 46}}, "23": {"start": {"line": 76, "column": 24}, "end": {"line": 76, "column": 25}}, "24": {"start": {"line": 78, "column": 28}, "end": {"line": 87, "column": 13}}, "25": {"start": {"line": 89, "column": 12}, "end": {"line": 89, "column": 42}}, "26": {"start": {"line": 90, "column": 12}, "end": {"line": 90, "column": 27}}, "27": {"start": {"line": 94, "column": 14}, "end": {"line": 94, "column": 30}}, "28": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 69}}, "29": {"start": {"line": 102, "column": 2}, "end": {"line": 110, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 26}, "end": {"line": 14, "column": 27}}, "loc": {"start": {"line": 22, "column": 21}, "end": {"line": 111, "column": 1}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 30}, "end": {"line": 23, "column": 31}}, "loc": {"start": {"line": 23, "column": 36}, "end": {"line": 23, "column": 65}}, "line": 23}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 28, "column": 4}, "end": {"line": 33, "column": 18}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 5}}, "loc": {"start": {"line": 39, "column": 6}, "end": {"line": 43, "column": 21}}, "line": 39}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 41, "column": 28}, "end": {"line": 41, "column": 29}}, "loc": {"start": {"line": 41, "column": 38}, "end": {"line": 41, "column": 50}}, "line": 41}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 5}}, "loc": {"start": {"line": 48, "column": 10}, "end": {"line": 48, "column": 83}}, "line": 48}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 52, "column": 33}, "end": {"line": 52, "column": 34}}, "loc": {"start": {"line": 52, "column": 39}, "end": {"line": 58, "column": 3}}, "line": 52}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 53, "column": 25}, "end": {"line": 53, "column": 26}}, "loc": {"start": {"line": 53, "column": 42}, "end": {"line": 53, "column": 78}}, "line": 53}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 5}}, "loc": {"start": {"line": 62, "column": 6}, "end": {"line": 98, "column": 8}}, "line": 62}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 62, "column": 18}, "end": {"line": 62, "column": 19}}, "loc": {"start": {"line": 62, "column": 33}, "end": {"line": 98, "column": 7}}, "line": 62}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 65, "column": 15}, "end": {"line": 65, "column": 16}}, "loc": {"start": {"line": 65, "column": 24}, "end": {"line": 91, "column": 11}}, "line": 65}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 93, "column": 12}, "end": {"line": 93, "column": 13}}, "loc": {"start": {"line": 94, "column": 14}, "end": {"line": 94, "column": 30}}, "line": 94}}, "branchMap": {"0": {"loc": {"start": {"line": 53, "column": 42}, "end": {"line": 53, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 53, "column": 61}, "end": {"line": 53, "column": 68}}, {"start": {"line": 53, "column": 71}, "end": {"line": 53, "column": 78}}], "line": 53}, "1": {"loc": {"start": {"line": 66, "column": 26}, "end": {"line": 66, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 27}, "end": {"line": 66, "column": 48}}, {"start": {"line": 66, "column": 53}, "end": {"line": 66, "column": 54}}], "line": 66}, "2": {"loc": {"start": {"line": 69, "column": 12}, "end": {"line": 71, "column": 13}}, "type": "if", "locations": [{"start": {"line": 69, "column": 12}, "end": {"line": 71, "column": 13}}, {"start": {}, "end": {}}], "line": 69}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useDropdown.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useDropdown.ts", "statementMap": {"0": {"start": {"line": 9, "column": 27}, "end": {"line": 40, "column": 1}}, "1": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 45}}, "2": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 50}}, "3": {"start": {"line": 13, "column": 25}, "end": {"line": 15, "column": 8}}, "4": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 31}}, "5": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 29}}, "6": {"start": {"line": 17, "column": 24}, "end": {"line": 19, "column": 8}}, "7": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 21}}, "8": {"start": {"line": 21, "column": 2}, "end": {"line": 37, "column": 22}}, "9": {"start": {"line": 22, "column": 31}, "end": {"line": 29, "column": 5}}, "10": {"start": {"line": 23, "column": 6}, "end": {"line": 28, "column": 7}}, "11": {"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": 24}}, "12": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 63}}, "13": {"start": {"line": 33, "column": 4}, "end": {"line": 36, "column": 6}}, "14": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 68}}, "15": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 49}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 28}}, "loc": {"start": {"line": 9, "column": 33}, "end": {"line": 40, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 37}, "end": {"line": 13, "column": 38}}, "loc": {"start": {"line": 13, "column": 43}, "end": {"line": 15, "column": 3}}, "line": 13}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 15}}, "loc": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 29}}, "line": 14}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 17, "column": 36}, "end": {"line": 17, "column": 37}}, "loc": {"start": {"line": 17, "column": 42}, "end": {"line": 19, "column": 3}}, "line": 17}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": 13}}, "loc": {"start": {"line": 21, "column": 18}, "end": {"line": 37, "column": 3}}, "line": 21}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 22, "column": 31}, "end": {"line": 22, "column": 32}}, "loc": {"start": {"line": 22, "column": 54}, "end": {"line": 29, "column": 5}}, "line": 22}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 33, "column": 11}, "end": {"line": 33, "column": 12}}, "loc": {"start": {"line": 33, "column": 17}, "end": {"line": 36, "column": 5}}, "line": 33}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 6}, "end": {"line": 28, "column": 7}}, "type": "if", "locations": [{"start": {"line": 23, "column": 6}, "end": {"line": 28, "column": 7}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 24, "column": 8}, "end": {"line": 25, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 27}}, {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 59}}], "line": 24}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useHighchartsDonut.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useHighchartsDonut.ts", "statementMap": {"0": {"start": {"line": 21, "column": 26}, "end": {"line": 60, "column": 2}}, "1": {"start": {"line": 21, "column": 60}, "end": {"line": 60, "column": 1}}, "2": {"start": {"line": 36, "column": 18}, "end": {"line": 36, "column": 28}}, "3": {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 39}}, "4": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 34}}, "5": {"start": {"line": 39, "column": 23}, "end": {"line": 39, "column": 25}}, "6": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 70}}, "7": {"start": {"line": 45, "column": 4}, "end": {"line": 50, "column": 5}}, "8": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 42}}, "9": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 29}}, "10": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 37}}, "11": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 32}}, "12": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 21}}, "13": {"start": {"line": 54, "column": 15}, "end": {"line": 54, "column": 21}}, "14": {"start": {"line": 55, "column": 4}, "end": {"line": 56, "column": 45}}, "15": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 45}}, "16": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 20}}, "17": {"start": {"line": 66, "column": 23}, "end": {"line": 99, "column": 2}}, "18": {"start": {"line": 66, "column": 58}, "end": {"line": 99, "column": 1}}, "19": {"start": {"line": 76, "column": 24}, "end": {"line": 78, "column": 11}}, "20": {"start": {"line": 79, "column": 10}, "end": {"line": 82, "column": 11}}, "21": {"start": {"line": 81, "column": 12}, "end": {"line": 81, "column": 53}}, "22": {"start": {"line": 101, "column": 34}, "end": {"line": 155, "column": 1}}, "23": {"start": {"line": 102, "column": 23}, "end": {"line": 109, "column": 3}}, "24": {"start": {"line": 112, "column": 18}, "end": {"line": 152, "column": 18}}, "25": {"start": {"line": 113, "column": 22}, "end": {"line": 118, "column": 7}}, "26": {"start": {"line": 113, "column": 42}, "end": {"line": 118, "column": 5}}, "27": {"start": {"line": 120, "column": 4}, "end": {"line": 151, "column": 6}}, "28": {"start": {"line": 154, "column": 2}, "end": {"line": 154, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 26}, "end": {"line": 21, "column": 27}}, "loc": {"start": {"line": 21, "column": 60}, "end": {"line": 60, "column": 1}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 35, "column": 14}, "end": {"line": 35, "column": 15}}, "loc": {"start": {"line": 35, "column": 55}, "end": {"line": 59, "column": 3}}, "line": 35}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 66, "column": 23}, "end": {"line": 66, "column": 24}}, "loc": {"start": {"line": 66, "column": 58}, "end": {"line": 99, "column": 1}}, "line": 66}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 16}}, "loc": {"start": {"line": 75, "column": 26}, "end": {"line": 83, "column": 9}}, "line": 75}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 101, "column": 34}, "end": {"line": 101, "column": 35}}, "loc": {"start": {"line": 101, "column": 79}, "end": {"line": 155, "column": 1}}, "line": 101}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 112, "column": 26}, "end": {"line": 112, "column": 27}}, "loc": {"start": {"line": 112, "column": 52}, "end": {"line": 152, "column": 3}}, "line": 112}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 113, "column": 31}, "end": {"line": 113, "column": 32}}, "loc": {"start": {"line": 113, "column": 42}, "end": {"line": 118, "column": 5}}, "line": 113}}, "branchMap": {"0": {"loc": {"start": {"line": 45, "column": 4}, "end": {"line": 50, "column": 5}}, "type": "if", "locations": [{"start": {"line": 45, "column": 4}, "end": {"line": 50, "column": 5}}, {"start": {"line": 47, "column": 11}, "end": {"line": 50, "column": 5}}], "line": 45}, "1": {"loc": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 21}}, "type": "if", "locations": [{"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 21}}, {"start": {}, "end": {}}], "line": 54}, "2": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 56, "column": 45}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 56, "column": 45}}, {"start": {}, "end": {}}], "line": 55}, "3": {"loc": {"start": {"line": 79, "column": 10}, "end": {"line": 82, "column": 11}}, "type": "if", "locations": [{"start": {"line": 79, "column": 10}, "end": {"line": 82, "column": 11}}, {"start": {}, "end": {}}], "line": 79}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useInfiniteQuery.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useInfiniteQuery.ts", "statementMap": {"0": {"start": {"line": 35, "column": 64}, "end": {"line": 35, "column": 77}}, "1": {"start": {"line": 36, "column": 30}, "end": {"line": 36, "column": 78}}, "2": {"start": {"line": 37, "column": 2}, "end": {"line": 41, "column": 22}}, "3": {"start": {"line": 38, "column": 4}, "end": {"line": 40, "column": 5}}, "4": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 31}}, "5": {"start": {"line": 43, "column": 26}, "end": {"line": 43, "column": 72}}, "6": {"start": {"line": 44, "column": 26}, "end": {"line": 44, "column": 47}}, "7": {"start": {"line": 45, "column": 36}, "end": {"line": 45, "column": 51}}, "8": {"start": {"line": 46, "column": 54}, "end": {"line": 46, "column": 69}}, "9": {"start": {"line": 47, "column": 40}, "end": {"line": 47, "column": 54}}, "10": {"start": {"line": 48, "column": 28}, "end": {"line": 48, "column": 56}}, "11": {"start": {"line": 55, "column": 28}, "end": {"line": 55, "column": 55}}, "12": {"start": {"line": 57, "column": 23}, "end": {"line": 93, "column": 3}}, "13": {"start": {"line": 59, "column": 26}, "end": {"line": 59, "column": 53}}, "14": {"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, "15": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 27}}, "16": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 36}}, "17": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 21}}, "18": {"start": {"line": 67, "column": 6}, "end": {"line": 90, "column": 7}}, "19": {"start": {"line": 68, "column": 25}, "end": {"line": 72, "column": 10}}, "20": {"start": {"line": 73, "column": 8}, "end": {"line": 77, "column": 10}}, "21": {"start": {"line": 74, "column": 10}, "end": {"line": 76, "column": 78}}, "22": {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 29}}, "23": {"start": {"line": 79, "column": 8}, "end": {"line": 81, "column": 10}}, "24": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 31}}, "25": {"start": {"line": 85, "column": 8}, "end": {"line": 89, "column": 9}}, "26": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 30}}, "27": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 39}}, "28": {"start": {"line": 95, "column": 24}, "end": {"line": 98, "column": 70}}, "29": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 64}}, "30": {"start": {"line": 96, "column": 57}, "end": {"line": 96, "column": 64}}, "31": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 27}}, "32": {"start": {"line": 100, "column": 20}, "end": {"line": 114, "column": 3}}, "33": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 23}}, "34": {"start": {"line": 102, "column": 25}, "end": {"line": 102, "column": 57}}, "35": {"start": {"line": 104, "column": 22}, "end": {"line": 104, "column": 54}}, "36": {"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": 50}}, "37": {"start": {"line": 106, "column": 4}, "end": {"line": 113, "column": 5}}, "38": {"start": {"line": 107, "column": 21}, "end": {"line": 107, "column": 91}}, "39": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 22}}, "40": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 29}}, "41": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 26}}, "42": {"start": {"line": 116, "column": 18}, "end": {"line": 121, "column": 3}}, "43": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 32}}, "44": {"start": {"line": 124, "column": 2}, "end": {"line": 126, "column": 15}}, "45": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 16}}, "46": {"start": {"line": 128, "column": 2}, "end": {"line": 136, "column": 4}}}, "fnMap": {"0": {"name": "useInfiniteQuery", "decl": {"start": {"line": 27, "column": 16}, "end": {"line": 27, "column": 32}}, "loc": {"start": {"line": 34, "column": 2}, "end": {"line": 137, "column": 1}}, "line": 34}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 13}}, "loc": {"start": {"line": 37, "column": 18}, "end": {"line": 41, "column": 3}}, "line": 37}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 5}}, "loc": {"start": {"line": 58, "column": 35}, "end": {"line": 91, "column": 5}}, "line": 58}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 73, "column": 16}, "end": {"line": 73, "column": 17}}, "loc": {"start": {"line": 74, "column": 10}, "end": {"line": 76, "column": 78}}, "line": 74}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 95, "column": 36}, "end": {"line": 95, "column": 37}}, "loc": {"start": {"line": 95, "column": 48}, "end": {"line": 98, "column": 3}}, "line": 95}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 100, "column": 20}, "end": {"line": 100, "column": 21}}, "loc": {"start": {"line": 100, "column": 51}, "end": {"line": 114, "column": 3}}, "line": 100}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 5}}, "loc": {"start": {"line": 117, "column": 29}, "end": {"line": 119, "column": 5}}, "line": 117}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 124, "column": 12}, "end": {"line": 124, "column": 13}}, "loc": {"start": {"line": 124, "column": 18}, "end": {"line": 126, "column": 3}}, "line": 124}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 45}}, "type": "default-arg", "locations": [{"start": {"line": 33, "column": 43}, "end": {"line": 33, "column": 45}}], "line": 33}, "1": {"loc": {"start": {"line": 35, "column": 16}, "end": {"line": 35, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 35, "column": 30}, "end": {"line": 35, "column": 31}}], "line": 35}, "2": {"loc": {"start": {"line": 35, "column": 33}, "end": {"line": 35, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 35, "column": 41}, "end": {"line": 35, "column": 43}}], "line": 35}, "3": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 40, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 40, "column": 5}}, {"start": {}, "end": {}}], "line": 38}, "4": {"loc": {"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, {"start": {"line": 62, "column": 13}, "end": {"line": 64, "column": 7}}], "line": 60}, "5": {"loc": {"start": {"line": 74, "column": 10}, "end": {"line": 76, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 14}, "end": {"line": 75, "column": 22}}, {"start": {"line": 76, "column": 14}, "end": {"line": 76, "column": 78}}], "line": 74}, "6": {"loc": {"start": {"line": 76, "column": 40}, "end": {"line": 76, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 76, "column": 40}, "end": {"line": 76, "column": 50}}, {"start": {"line": 76, "column": 54}, "end": {"line": 76, "column": 56}}], "line": 76}, "7": {"loc": {"start": {"line": 85, "column": 8}, "end": {"line": 89, "column": 9}}, "type": "if", "locations": [{"start": {"line": 85, "column": 8}, "end": {"line": 89, "column": 9}}, {"start": {"line": 87, "column": 15}, "end": {"line": 89, "column": 9}}], "line": 85}, "8": {"loc": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 64}}, "type": "if", "locations": [{"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 64}}, {"start": {}, "end": {}}], "line": 96}, "9": {"loc": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 20}}, {"start": {"line": 96, "column": 24}, "end": {"line": 96, "column": 42}}, {"start": {"line": 96, "column": 46}, "end": {"line": 96, "column": 55}}], "line": 96}, "10": {"loc": {"start": {"line": 100, "column": 27}, "end": {"line": 100, "column": 46}}, "type": "default-arg", "locations": [{"start": {"line": 100, "column": 44}, "end": {"line": 100, "column": 46}}], "line": 100}, "11": {"loc": {"start": {"line": 104, "column": 22}, "end": {"line": 104, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 104, "column": 22}, "end": {"line": 104, "column": 39}}, {"start": {"line": 104, "column": 43}, "end": {"line": 104, "column": 54}}], "line": 104}, "12": {"loc": {"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": 41}}, {"start": {"line": 105, "column": 45}, "end": {"line": 105, "column": 50}}], "line": 105}, "13": {"loc": {"start": {"line": 117, "column": 5}, "end": {"line": 117, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 117, "column": 22}, "end": {"line": 117, "column": 24}}], "line": 117}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0, 0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useInfiniteScroll.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/hooks/useInfiniteScroll.ts", "statementMap": {"0": {"start": {"line": 12, "column": 33}, "end": {"line": 54, "column": 1}}, "1": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 51}}, "2": {"start": {"line": 20, "column": 23}, "end": {"line": 32, "column": 54}}, "3": {"start": {"line": 21, "column": 20}, "end": {"line": 21, "column": 40}}, "4": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 63}}, "5": {"start": {"line": 22, "column": 56}, "end": {"line": 22, "column": 63}}, "6": {"start": {"line": 24, "column": 54}, "end": {"line": 24, "column": 61}}, "7": {"start": {"line": 25, "column": 22}, "end": {"line": 25, "column": 25}}, "8": {"start": {"line": 27, "column": 4}, "end": {"line": 31, "column": 5}}, "9": {"start": {"line": 28, "column": 6}, "end": {"line": 30, "column": 7}}, "10": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 24}}, "11": {"start": {"line": 35, "column": 2}, "end": {"line": 51, "column": 67}}, "12": {"start": {"line": 36, "column": 26}, "end": {"line": 46, "column": 5}}, "13": {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 42}}, "14": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 65}}, "15": {"start": {"line": 38, "column": 58}, "end": {"line": 38, "column": 65}}, "16": {"start": {"line": 40, "column": 29}, "end": {"line": 40, "column": 73}}, "17": {"start": {"line": 41, "column": 6}, "end": {"line": 45, "column": 7}}, "18": {"start": {"line": 42, "column": 8}, "end": {"line": 44, "column": 9}}, "19": {"start": {"line": 43, "column": 10}, "end": {"line": 43, "column": 26}}, "20": {"start": {"line": 49, "column": 18}, "end": {"line": 49, "column": 48}}, "21": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 37}}, "22": {"start": {"line": 50, "column": 17}, "end": {"line": 50, "column": 36}}, "23": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 40}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 33}, "end": {"line": 12, "column": 34}}, "loc": {"start": {"line": 17, "column": 30}, "end": {"line": 54, "column": 1}}, "line": 17}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 35}, "end": {"line": 20, "column": 36}}, "loc": {"start": {"line": 20, "column": 41}, "end": {"line": 32, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 13}}, "loc": {"start": {"line": 35, "column": 18}, "end": {"line": 51, "column": 3}}, "line": 35}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 36, "column": 26}, "end": {"line": 36, "column": 27}}, "loc": {"start": {"line": 36, "column": 32}, "end": {"line": 46, "column": 5}}, "line": 36}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 50, "column": 11}, "end": {"line": 50, "column": 12}}, "loc": {"start": {"line": 50, "column": 17}, "end": {"line": 50, "column": 36}}, "line": 50}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 63}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 63}}, {"start": {}, "end": {}}], "line": 22}, "1": {"loc": {"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 16}}, {"start": {"line": 22, "column": 20}, "end": {"line": 22, "column": 32}}, {"start": {"line": 22, "column": 36}, "end": {"line": 22, "column": 54}}], "line": 22}, "2": {"loc": {"start": {"line": 27, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 27, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {}, "end": {}}], "line": 27}, "3": {"loc": {"start": {"line": 28, "column": 6}, "end": {"line": 30, "column": 7}}, "type": "if", "locations": [{"start": {"line": 28, "column": 6}, "end": {"line": 30, "column": 7}}, {"start": {}, "end": {}}], "line": 28}, "4": {"loc": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 65}}, "type": "if", "locations": [{"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 65}}, {"start": {}, "end": {}}], "line": 38}, "5": {"loc": {"start": {"line": 38, "column": 10}, "end": {"line": 38, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 10}, "end": {"line": 38, "column": 18}}, {"start": {"line": 38, "column": 22}, "end": {"line": 38, "column": 34}}, {"start": {"line": 38, "column": 38}, "end": {"line": 38, "column": 56}}], "line": 38}, "6": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 45, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 45, "column": 7}}, {"start": {}, "end": {}}], "line": 41}, "7": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 44, "column": 9}}, "type": "if", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 44, "column": 9}}, {"start": {}, "end": {}}], "line": 42}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0, 0], "6": [0, 0], "7": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdown.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdown.tsx", "statementMap": {"0": {"start": {"line": 22, "column": 50}, "end": {"line": 22, "column": 63}}, "1": {"start": {"line": 23, "column": 38}, "end": {"line": 23, "column": 50}}, "2": {"start": {"line": 25, "column": 56}, "end": {"line": 47, "column": 41}}, "3": {"start": {"line": 26, "column": 32}, "end": {"line": 26, "column": 56}}, "4": {"start": {"line": 27, "column": 21}, "end": {"line": 34, "column": 50}}, "5": {"start": {"line": 28, "column": 36}, "end": {"line": 33, "column": 7}}, "6": {"start": {"line": 31, "column": 10}, "end": {"line": 31, "column": 65}}, "7": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 49}}, "8": {"start": {"line": 36, "column": 16}, "end": {"line": 38, "column": 5}}, "9": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 34}}, "10": {"start": {"line": 37, "column": 27}, "end": {"line": 37, "column": 33}}, "11": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 75}}, "12": {"start": {"line": 40, "column": 45}, "end": {"line": 40, "column": 74}}, "13": {"start": {"line": 42, "column": 4}, "end": {"line": 46, "column": 6}}, "14": {"start": {"line": 49, "column": 29}, "end": {"line": 57, "column": 3}}, "15": {"start": {"line": 51, "column": 26}, "end": {"line": 53, "column": 40}}, "16": {"start": {"line": 52, "column": 38}, "end": {"line": 52, "column": 54}}, "17": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 37}}, "18": {"start": {"line": 59, "column": 28}, "end": {"line": 71, "column": 3}}, "19": {"start": {"line": 61, "column": 31}, "end": {"line": 61, "column": 63}}, "20": {"start": {"line": 61, "column": 56}, "end": {"line": 61, "column": 62}}, "21": {"start": {"line": 62, "column": 33}, "end": {"line": 64, "column": 7}}, "22": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 37}}, "23": {"start": {"line": 65, "column": 26}, "end": {"line": 67, "column": 70}}, "24": {"start": {"line": 66, "column": 38}, "end": {"line": 66, "column": 67}}, "25": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 37}}, "26": {"start": {"line": 73, "column": 26}, "end": {"line": 75, "column": 52}}, "27": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 55}}, "28": {"start": {"line": 77, "column": 22}, "end": {"line": 101, "column": 34}}, "29": {"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, "30": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 70}}, "31": {"start": {"line": 82, "column": 23}, "end": {"line": 82, "column": 48}}, "32": {"start": {"line": 83, "column": 24}, "end": {"line": 83, "column": 57}}, "33": {"start": {"line": 85, "column": 4}, "end": {"line": 98, "column": 5}}, "34": {"start": {"line": 86, "column": 29}, "end": {"line": 86, "column": 74}}, "35": {"start": {"line": 87, "column": 24}, "end": {"line": 87, "column": 57}}, "36": {"start": {"line": 88, "column": 29}, "end": {"line": 88, "column": 53}}, "37": {"start": {"line": 90, "column": 6}, "end": {"line": 97, "column": 8}}, "38": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 77}}, "39": {"start": {"line": 103, "column": 2}, "end": {"line": 135, "column": 4}}}, "fnMap": {"0": {"name": "CardDropdown", "decl": {"start": {"line": 13, "column": 16}, "end": {"line": 13, "column": 28}}, "loc": {"start": {"line": 21, "column": 32}, "end": {"line": 136, "column": 1}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 64}, "end": {"line": 25, "column": 65}}, "loc": {"start": {"line": 25, "column": 70}, "end": {"line": 47, "column": 3}}, "line": 25}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 11}, "end": {"line": 28, "column": 12}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 33, "column": 7}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 30, "column": 38}, "end": {"line": 30, "column": 39}}, "loc": {"start": {"line": 31, "column": 10}, "end": {"line": 31, "column": 65}}, "line": 31}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 15}}, "loc": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 49}}, "line": 34}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 36, "column": 31}, "end": {"line": 36, "column": 32}}, "loc": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 34}}, "line": 37}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 21}}, "loc": {"start": {"line": 37, "column": 27}, "end": {"line": 37, "column": 33}}, "line": 37}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 40, "column": 34}, "end": {"line": 40, "column": 35}}, "loc": {"start": {"line": 40, "column": 45}, "end": {"line": 40, "column": 74}}, "line": 40}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 5}}, "loc": {"start": {"line": 50, "column": 28}, "end": {"line": 55, "column": 5}}, "line": 50}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 52, "column": 31}, "end": {"line": 52, "column": 32}}, "loc": {"start": {"line": 52, "column": 38}, "end": {"line": 52, "column": 54}}, "line": 52}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 5}}, "loc": {"start": {"line": 60, "column": 28}, "end": {"line": 69, "column": 5}}, "line": 60}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 61, "column": 49}, "end": {"line": 61, "column": 50}}, "loc": {"start": {"line": 61, "column": 56}, "end": {"line": 61, "column": 62}}, "line": 61}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 62, "column": 56}, "end": {"line": 62, "column": 57}}, "loc": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 37}}, "line": 63}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 66, "column": 31}, "end": {"line": 66, "column": 32}}, "loc": {"start": {"line": 66, "column": 38}, "end": {"line": 66, "column": 67}}, "line": 66}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 73, "column": 38}, "end": {"line": 73, "column": 39}}, "loc": {"start": {"line": 73, "column": 44}, "end": {"line": 75, "column": 3}}, "line": 73}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 77, "column": 30}, "end": {"line": 77, "column": 31}}, "loc": {"start": {"line": 77, "column": 36}, "end": {"line": 101, "column": 3}}, "line": 77}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 11}, "end": {"line": 14, "column": 13}}], "line": 14}, "1": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 16}, "end": {"line": 17, "column": 28}}], "line": 17}, "2": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 23}, "end": {"line": 19, "column": 27}}], "line": 19}, "3": {"loc": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 23}, "end": {"line": 20, "column": 27}}], "line": 20}, "4": {"loc": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 20}}, {"start": {"line": 40, "column": 24}, "end": {"line": 40, "column": 75}}], "line": 40}, "5": {"loc": {"start": {"line": 51, "column": 26}, "end": {"line": 53, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 52, "column": 10}, "end": {"line": 52, "column": 55}}, {"start": {"line": 53, "column": 10}, "end": {"line": 53, "column": 40}}], "line": 51}, "6": {"loc": {"start": {"line": 65, "column": 26}, "end": {"line": 67, "column": 70}}, "type": "cond-expr", "locations": [{"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 68}}, {"start": {"line": 67, "column": 10}, "end": {"line": 67, "column": 70}}], "line": 65}, "7": {"loc": {"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 53}}, "type": "cond-expr", "locations": [{"start": {"line": 74, "column": 38}, "end": {"line": 74, "column": 40}}, {"start": {"line": 74, "column": 43}, "end": {"line": 74, "column": 53}}], "line": 74}, "8": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, {"start": {}, "end": {}}], "line": 78}, "9": {"loc": {"start": {"line": 82, "column": 23}, "end": {"line": 82, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 82, "column": 43}, "end": {"line": 82, "column": 44}}, {"start": {"line": 82, "column": 47}, "end": {"line": 82, "column": 48}}], "line": 82}, "10": {"loc": {"start": {"line": 85, "column": 4}, "end": {"line": 98, "column": 5}}, "type": "if", "locations": [{"start": {"line": 85, "column": 4}, "end": {"line": 98, "column": 5}}, {"start": {}, "end": {}}], "line": 85}, "11": {"loc": {"start": {"line": 120, "column": 7}, "end": {"line": 133, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 120, "column": 7}, "end": {"line": 120, "column": 13}}, {"start": {"line": 121, "column": 8}, "end": {"line": 132, "column": 10}}], "line": 120}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdownMenu.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdownMenu.tsx", "statementMap": {"0": {"start": {"line": 14, "column": 48}, "end": {"line": 28, "column": 1}}, "1": {"start": {"line": 16, "column": 4}, "end": {"line": 26, "column": 10}}, "2": {"start": {"line": 22, "column": 25}, "end": {"line": 22, "column": 55}}, "3": {"start": {"line": 32, "column": 44}, "end": {"line": 72, "column": 1}}, "4": {"start": {"line": 38, "column": 2}, "end": {"line": 71, "column": 8}}, "5": {"start": {"line": 40, "column": 6}, "end": {"line": 69, "column": 12}}, "6": {"start": {"line": 43, "column": 12}, "end": {"line": 66, "column": 17}}, "7": {"start": {"line": 46, "column": 29}, "end": {"line": 46, "column": 56}}, "8": {"start": {"line": 48, "column": 16}, "end": {"line": 50, "column": 17}}, "9": {"start": {"line": 49, "column": 18}, "end": {"line": 49, "column": 46}}, "10": {"start": {"line": 76, "column": 54}, "end": {"line": 84, "column": 1}}, "11": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 10}}, "12": {"start": {"line": 88, "column": 65}, "end": {"line": 106, "column": 1}}, "13": {"start": {"line": 89, "column": 2}, "end": {"line": 105, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 3}}, "loc": {"start": {"line": 16, "column": 4}, "end": {"line": 26, "column": 10}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 18}, "end": {"line": 22, "column": 19}}, "loc": {"start": {"line": 22, "column": 25}, "end": {"line": 22, "column": 55}}, "line": 22}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 44}, "end": {"line": 32, "column": 45}}, "loc": {"start": {"line": 38, "column": 2}, "end": {"line": 71, "column": 8}}, "line": 38}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 39, "column": 24}, "end": {"line": 39, "column": 25}}, "loc": {"start": {"line": 40, "column": 6}, "end": {"line": 69, "column": 12}}, "line": 40}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 42, "column": 29}, "end": {"line": 42, "column": 30}}, "loc": {"start": {"line": 43, "column": 12}, "end": {"line": 66, "column": 17}}, "line": 43}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 46, "column": 23}, "end": {"line": 46, "column": 24}}, "loc": {"start": {"line": 46, "column": 29}, "end": {"line": 46, "column": 56}}, "line": 46}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 47, "column": 25}, "end": {"line": 47, "column": 26}}, "loc": {"start": {"line": 47, "column": 32}, "end": {"line": 51, "column": 15}}, "line": 47}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 3}}, "loc": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 10}}, "line": 78}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 88, "column": 65}, "end": {"line": 88, "column": 66}}, "loc": {"start": {"line": 89, "column": 2}, "end": {"line": 105, "column": 8}}, "line": 89}}, "branchMap": {"0": {"loc": {"start": {"line": 48, "column": 16}, "end": {"line": 50, "column": 17}}, "type": "if", "locations": [{"start": {"line": 48, "column": 16}, "end": {"line": 50, "column": 17}}, {"start": {}, "end": {}}], "line": 48}, "1": {"loc": {"start": {"line": 48, "column": 20}, "end": {"line": 48, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 20}, "end": {"line": 48, "column": 37}}, {"start": {"line": 48, "column": 41}, "end": {"line": 48, "column": 54}}], "line": 48}, "2": {"loc": {"start": {"line": 58, "column": 19}, "end": {"line": 62, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 59, "column": 20}, "end": {"line": 59, "column": 37}}, {"start": {"line": 61, "column": 20}, "end": {"line": 61, "column": 39}}], "line": 58}, "3": {"loc": {"start": {"line": 80, "column": 18}, "end": {"line": 80, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 80, "column": 34}, "end": {"line": 80, "column": 48}}, {"start": {"line": 80, "column": 51}, "end": {"line": 80, "column": 63}}], "line": 80}, "4": {"loc": {"start": {"line": 90, "column": 5}, "end": {"line": 95, "column": 5}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 5}, "end": {"line": 90, "column": 29}}, {"start": {"line": 91, "column": 6}, "end": {"line": 94, "column": 8}}], "line": 90}, "5": {"loc": {"start": {"line": 99, "column": 5}, "end": {"line": 104, "column": 5}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 5}, "end": {"line": 99, "column": 29}}, {"start": {"line": 100, "column": 6}, "end": {"line": 103, "column": 8}}], "line": 99}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdownSelectors.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardDropdownSelectors.tsx", "statementMap": {"0": {"start": {"line": 16, "column": 75}, "end": {"line": 52, "column": 1}}, "1": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "2": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 16}}, "3": {"start": {"line": 25, "column": 2}, "end": {"line": 51, "column": 4}}, "4": {"start": {"line": 28, "column": 8}, "end": {"line": 30, "column": 9}}, "5": {"start": {"line": 29, "column": 10}, "end": {"line": 29, "column": 22}}, "6": {"start": {"line": 31, "column": 8}, "end": {"line": 48, "column": 10}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 16, "column": 75}, "end": {"line": 16, "column": 76}}, "loc": {"start": {"line": 20, "column": 6}, "end": {"line": 52, "column": 1}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 24}, "end": {"line": 27, "column": 25}}, "loc": {"start": {"line": 27, "column": 51}, "end": {"line": 49, "column": 7}}, "line": 27}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, {"start": {}, "end": {}}], "line": 21}, "1": {"loc": {"start": {"line": 28, "column": 8}, "end": {"line": 30, "column": 9}}, "type": "if", "locations": [{"start": {"line": 28, "column": 8}, "end": {"line": 30, "column": 9}}, {"start": {}, "end": {}}], "line": 28}, "2": {"loc": {"start": {"line": 36, "column": 17}, "end": {"line": 36, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 17}, "end": {"line": 36, "column": 35}}, {"start": {"line": 36, "column": 39}, "end": {"line": 36, "column": 52}}], "line": 36}, "3": {"loc": {"start": {"line": 42, "column": 27}, "end": {"line": 42, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 27}, "end": {"line": 42, "column": 54}}, {"start": {"line": 42, "column": 58}, "end": {"line": 42, "column": 60}}], "line": 42}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardGrid.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardGrid.tsx", "statementMap": {"0": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": 46}}, "1": {"start": {"line": 24, "column": 41}, "end": {"line": 29, "column": 4}}, "2": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 47}}, "3": {"start": {"line": 32, "column": 24}, "end": {"line": 68, "column": 3}}, "4": {"start": {"line": 34, "column": 4}, "end": {"line": 39, "column": 5}}, "5": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "6": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 55}}, "7": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 74}}, "8": {"start": {"line": 42, "column": 25}, "end": {"line": 50, "column": 6}}, "9": {"start": {"line": 43, "column": 54}, "end": {"line": 43, "column": 75}}, "10": {"start": {"line": 44, "column": 6}, "end": {"line": 48, "column": 11}}, "11": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 55}}, "12": {"start": {"line": 47, "column": 10}, "end": {"line": 47, "column": 50}}, "13": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 20}}, "14": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, "15": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 69}}, "16": {"start": {"line": 56, "column": 4}, "end": {"line": 67, "column": 6}}, "17": {"start": {"line": 60, "column": 51}, "end": {"line": 60, "column": 83}}, "18": {"start": {"line": 70, "column": 2}, "end": {"line": 74, "column": 4}}}, "fnMap": {"0": {"name": "CardGrid", "decl": {"start": {"line": 9, "column": 24}, "end": {"line": 9, "column": 32}}, "loc": {"start": {"line": 20, "column": 28}, "end": {"line": 75, "column": 1}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 32, "column": 24}, "end": {"line": 32, "column": 25}}, "loc": {"start": {"line": 32, "column": 30}, "end": {"line": 68, "column": 3}}, "line": 32}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 37}, "end": {"line": 42, "column": 38}}, "loc": {"start": {"line": 42, "column": 49}, "end": {"line": 50, "column": 5}}, "line": 42}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 45, "column": 16}, "end": {"line": 45, "column": 17}}, "loc": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 55}}, "line": 45}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 46, "column": 17}, "end": {"line": 46, "column": 18}}, "loc": {"start": {"line": 46, "column": 34}, "end": {"line": 48, "column": 9}}, "line": 46}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 60, "column": 44}, "end": {"line": 60, "column": 45}}, "loc": {"start": {"line": 60, "column": 51}, "end": {"line": 60, "column": 83}}, "line": 60}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 23}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 18}, "end": {"line": 18, "column": 23}}], "line": 18}, "1": {"loc": {"start": {"line": 28, "column": 16}, "end": {"line": 28, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 16}, "end": {"line": 28, "column": 31}}, {"start": {"line": 28, "column": 35}, "end": {"line": 28, "column": 36}}], "line": 28}, "2": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 39, "column": 5}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 39, "column": 5}}, {"start": {}, "end": {}}], "line": 34}, "3": {"loc": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "type": "if", "locations": [{"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, {"start": {}, "end": {}}], "line": 35}, "4": {"loc": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 37}}, {"start": {"line": 45, "column": 41}, "end": {"line": 45, "column": 55}}], "line": 45}, "5": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, {"start": {}, "end": {}}], "line": 52}, "6": {"loc": {"start": {"line": 60, "column": 51}, "end": {"line": 60, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 51}, "end": {"line": 60, "column": 65}}, {"start": {"line": 60, "column": 69}, "end": {"line": 60, "column": 83}}], "line": 60}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardModule.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardModule.tsx", "statementMap": {"0": {"start": {"line": 77, "column": 34}, "end": {"line": 79, "column": 3}}, "1": {"start": {"line": 80, "column": 36}, "end": {"line": 85, "column": 4}}, "2": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "3": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 19}}, "4": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 36}}, "5": {"start": {"line": 86, "column": 40}, "end": {"line": 86, "column": 55}}, "6": {"start": {"line": 87, "column": 40}, "end": {"line": 87, "column": 60}}, "7": {"start": {"line": 88, "column": 42}, "end": {"line": 90, "column": 3}}, "8": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 36}}, "9": {"start": {"line": 89, "column": 33}, "end": {"line": 89, "column": 35}}, "10": {"start": {"line": 92, "column": 26}, "end": {"line": 128, "column": 40}}, "11": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 28}}, "12": {"start": {"line": 93, "column": 18}, "end": {"line": 93, "column": 28}}, "13": {"start": {"line": 95, "column": 23}, "end": {"line": 95, "column": 35}}, "14": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": 5}}, "15": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 78}}, "16": {"start": {"line": 99, "column": 56}, "end": {"line": 99, "column": 76}}, "17": {"start": {"line": 103, "column": 33}, "end": {"line": 103, "column": 48}}, "18": {"start": {"line": 104, "column": 4}, "end": {"line": 117, "column": 5}}, "19": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 69}}, "20": {"start": {"line": 106, "column": 53}, "end": {"line": 106, "column": 62}}, "21": {"start": {"line": 107, "column": 40}, "end": {"line": 111, "column": 7}}, "22": {"start": {"line": 109, "column": 27}, "end": {"line": 109, "column": 66}}, "23": {"start": {"line": 110, "column": 24}, "end": {"line": 110, "column": 42}}, "24": {"start": {"line": 112, "column": 6}, "end": {"line": 116, "column": 7}}, "25": {"start": {"line": 113, "column": 8}, "end": {"line": 115, "column": 10}}, "26": {"start": {"line": 114, "column": 10}, "end": {"line": 114, "column": 62}}, "27": {"start": {"line": 120, "column": 30}, "end": {"line": 120, "column": 45}}, "28": {"start": {"line": 121, "column": 4}, "end": {"line": 125, "column": 5}}, "29": {"start": {"line": 122, "column": 6}, "end": {"line": 124, "column": 9}}, "30": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 59}}, "31": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 24}}, "32": {"start": {"line": 130, "column": 24}, "end": {"line": 133, "column": 17}}, "33": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 16}}, "34": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 31}}, "35": {"start": {"line": 135, "column": 29}, "end": {"line": 144, "column": 3}}, "36": {"start": {"line": 137, "column": 6}, "end": {"line": 141, "column": 9}}, "37": {"start": {"line": 138, "column": 26}, "end": {"line": 138, "column": 41}}, "38": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 39}}, "39": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 25}}, "40": {"start": {"line": 146, "column": 25}, "end": {"line": 146, "column": 70}}, "41": {"start": {"line": 147, "column": 35}, "end": {"line": 149, "column": 11}}, "42": {"start": {"line": 150, "column": 36}, "end": {"line": 152, "column": 11}}, "43": {"start": {"line": 154, "column": 28}, "end": {"line": 181, "column": 5}}, "44": {"start": {"line": 155, "column": 4}, "end": {"line": 181, "column": 5}}, "45": {"start": {"line": 183, "column": 27}, "end": {"line": 247, "column": 3}}, "46": {"start": {"line": 184, "column": 4}, "end": {"line": 246, "column": 7}}, "47": {"start": {"line": 192, "column": 29}, "end": {"line": 192, "column": 57}}, "48": {"start": {"line": 249, "column": 2}, "end": {"line": 267, "column": 4}}, "49": {"start": {"line": 260, "column": 25}, "end": {"line": 260, "column": 46}}}, "fnMap": {"0": {"name": "CardModule", "decl": {"start": {"line": 60, "column": 24}, "end": {"line": 60, "column": 34}}, "loc": {"start": {"line": 76, "column": 30}, "end": {"line": 268, "column": 1}}, "line": 76}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 80, "column": 53}, "end": {"line": 80, "column": 54}}, "loc": {"start": {"line": 80, "column": 59}, "end": {"line": 85, "column": 3}}, "line": 80}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 88, "column": 63}, "end": {"line": 88, "column": 64}}, "loc": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 36}}, "line": 89}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 89, "column": 27}, "end": {"line": 89, "column": 28}}, "loc": {"start": {"line": 89, "column": 33}, "end": {"line": 89, "column": 35}}, "line": 89}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 92, "column": 34}, "end": {"line": 92, "column": 35}}, "loc": {"start": {"line": 92, "column": 40}, "end": {"line": 128, "column": 3}}, "line": 92}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 99, "column": 41}, "end": {"line": 99, "column": 42}}, "loc": {"start": {"line": 99, "column": 56}, "end": {"line": 99, "column": 76}}, "line": 99}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 106, "column": 46}, "end": {"line": 106, "column": 47}}, "loc": {"start": {"line": 106, "column": 53}, "end": {"line": 106, "column": 62}}, "line": 106}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 109, "column": 18}, "end": {"line": 109, "column": 19}}, "loc": {"start": {"line": 109, "column": 27}, "end": {"line": 109, "column": 66}}, "line": 109}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 110, "column": 15}, "end": {"line": 110, "column": 16}}, "loc": {"start": {"line": 110, "column": 24}, "end": {"line": 110, "column": 42}}, "line": 110}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 113, "column": 43}, "end": {"line": 113, "column": 44}}, "loc": {"start": {"line": 114, "column": 10}, "end": {"line": 114, "column": 62}}, "line": 114}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 122, "column": 41}, "end": {"line": 122, "column": 42}}, "loc": {"start": {"line": 122, "column": 61}, "end": {"line": 124, "column": 7}}, "line": 122}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 130, "column": 36}, "end": {"line": 130, "column": 37}}, "loc": {"start": {"line": 130, "column": 42}, "end": {"line": 133, "column": 3}}, "line": 130}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 5}}, "loc": {"start": {"line": 136, "column": 46}, "end": {"line": 142, "column": 5}}, "line": 136}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 137, "column": 22}, "end": {"line": 137, "column": 23}}, "loc": {"start": {"line": 137, "column": 38}, "end": {"line": 141, "column": 7}}, "line": 137}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 154, "column": 28}, "end": {"line": 154, "column": 29}}, "loc": {"start": {"line": 155, "column": 4}, "end": {"line": 181, "column": 5}}, "line": 155}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 183, "column": 27}, "end": {"line": 183, "column": 28}}, "loc": {"start": {"line": 184, "column": 4}, "end": {"line": 246, "column": 7}}, "line": 184}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 192, "column": 23}, "end": {"line": 192, "column": 24}}, "loc": {"start": {"line": 192, "column": 29}, "end": {"line": 192, "column": 57}}, "line": 192}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 260, "column": 19}, "end": {"line": 260, "column": 20}}, "loc": {"start": {"line": 260, "column": 25}, "end": {"line": 260, "column": 46}}, "line": 260}}, "branchMap": {"0": {"loc": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 25}}, "type": "default-arg", "locations": [{"start": {"line": 65, "column": 23}, "end": {"line": 65, "column": 25}}], "line": 65}, "1": {"loc": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 16}}], "line": 67}, "2": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 35}}, {"start": {"line": 78, "column": 39}, "end": {"line": 78, "column": 45}}], "line": 78}, "3": {"loc": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "type": "if", "locations": [{"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, {"start": {}, "end": {}}], "line": 81}, "4": {"loc": {"start": {"line": 84, "column": 11}, "end": {"line": 84, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 11}, "end": {"line": 84, "column": 29}}, {"start": {"line": 84, "column": 33}, "end": {"line": 84, "column": 35}}], "line": 84}, "5": {"loc": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 28}}, "type": "if", "locations": [{"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 28}}, {"start": {}, "end": {}}], "line": 93}, "6": {"loc": {"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": 5}}, "type": "if", "locations": [{"start": {"line": 98, "column": 4}, "end": {"line": 100, "column": 5}}, {"start": {}, "end": {}}], "line": 98}, "7": {"loc": {"start": {"line": 104, "column": 4}, "end": {"line": 117, "column": 5}}, "type": "if", "locations": [{"start": {"line": 104, "column": 4}, "end": {"line": 117, "column": 5}}, {"start": {}, "end": {}}], "line": 104}, "8": {"loc": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 63}}, {"start": {"line": 106, "column": 67}, "end": {"line": 106, "column": 69}}], "line": 106}, "9": {"loc": {"start": {"line": 112, "column": 6}, "end": {"line": 116, "column": 7}}, "type": "if", "locations": [{"start": {"line": 112, "column": 6}, "end": {"line": 116, "column": 7}}, {"start": {}, "end": {}}], "line": 112}, "10": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 125, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 125, "column": 5}}, {"start": {}, "end": {}}], "line": 121}, "11": {"loc": {"start": {"line": 146, "column": 25}, "end": {"line": 146, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 146, "column": 25}, "end": {"line": 146, "column": 40}}, {"start": {"line": 146, "column": 44}, "end": {"line": 146, "column": 70}}], "line": 146}, "12": {"loc": {"start": {"line": 147, "column": 35}, "end": {"line": 149, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 148, "column": 6}, "end": {"line": 148, "column": 39}}, {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 11}}], "line": 147}, "13": {"loc": {"start": {"line": 150, "column": 36}, "end": {"line": 152, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 40}}, {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 11}}], "line": 150}, "14": {"loc": {"start": {"line": 155, "column": 4}, "end": {"line": 181, "column": 5}}, "type": "cond-expr", "locations": [{"start": {"line": 156, "column": 6}, "end": {"line": 165, "column": 8}}, {"start": {"line": 167, "column": 6}, "end": {"line": 180, "column": 8}}], "line": 155}, "15": {"loc": {"start": {"line": 162, "column": 28}, "end": {"line": 162, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 162, "column": 28}, "end": {"line": 162, "column": 46}}, {"start": {"line": 162, "column": 50}, "end": {"line": 162, "column": 55}}], "line": 162}, "16": {"loc": {"start": {"line": 163, "column": 19}, "end": {"line": 163, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 163, "column": 19}, "end": {"line": 163, "column": 28}}, {"start": {"line": 163, "column": 32}, "end": {"line": 163, "column": 37}}], "line": 163}, "17": {"loc": {"start": {"line": 195, "column": 7}, "end": {"line": 213, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 195, "column": 7}, "end": {"line": 195, "column": 41}}, {"start": {"line": 196, "column": 8}, "end": {"line": 212, "column": 14}}], "line": 195}, "18": {"loc": {"start": {"line": 208, "column": 13}, "end": {"line": 210, "column": 13}}, "type": "binary-expr", "locations": [{"start": {"line": 208, "column": 13}, "end": {"line": 208, "column": 48}}, {"start": {"line": 209, "column": 14}, "end": {"line": 209, "column": 78}}], "line": 208}, "19": {"loc": {"start": {"line": 215, "column": 7}, "end": {"line": 223, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 215, "column": 7}, "end": {"line": 215, "column": 42}}, {"start": {"line": 216, "column": 8}, "end": {"line": 216, "column": 55}}, {"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 27}}, {"start": {"line": 218, "column": 10}, "end": {"line": 222, "column": 12}}], "line": 215}, "20": {"loc": {"start": {"line": 225, "column": 7}, "end": {"line": 233, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 225, "column": 7}, "end": {"line": 225, "column": 37}}, {"start": {"line": 226, "column": 8}, "end": {"line": 232, "column": 10}}], "line": 225}, "21": {"loc": {"start": {"line": 235, "column": 7}, "end": {"line": 243, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 235, "column": 7}, "end": {"line": 235, "column": 42}}, {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 54}}, {"start": {"line": 237, "column": 8}, "end": {"line": 237, "column": 27}}, {"start": {"line": 238, "column": 10}, "end": {"line": 242, "column": 12}}], "line": 235}, "22": {"loc": {"start": {"line": 257, "column": 7}, "end": {"line": 265, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 257, "column": 7}, "end": {"line": 257, "column": 18}}, {"start": {"line": 258, "column": 8}, "end": {"line": 264, "column": 22}}], "line": 257}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0, 0, 0], "20": [0, 0], "21": [0, 0, 0, 0], "22": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardModuleHeader.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardModuleHeader.tsx", "statementMap": {"0": {"start": {"line": 17, "column": 65}, "end": {"line": 70, "column": 1}}, "1": {"start": {"line": 26, "column": 2}, "end": {"line": 69, "column": 4}}, "2": {"start": {"line": 33, "column": 29}, "end": {"line": 33, "column": 53}}, "3": {"start": {"line": 42, "column": 29}, "end": {"line": 42, "column": 53}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 17, "column": 65}, "end": {"line": 17, "column": 66}}, "loc": {"start": {"line": 25, "column": 6}, "end": {"line": 70, "column": 1}}, "line": 25}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 24}}, "loc": {"start": {"line": 33, "column": 29}, "end": {"line": 33, "column": 53}}, "line": 33}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": 24}}, "loc": {"start": {"line": 42, "column": 29}, "end": {"line": 42, "column": 53}}, "line": 42}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 28}}], "line": 21}, "1": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 22, "column": 25}, "end": {"line": 22, "column": 29}}], "line": 22}, "2": {"loc": {"start": {"line": 30, "column": 9}, "end": {"line": 51, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 30, "column": 9}, "end": {"line": 30, "column": 28}}, {"start": {"line": 31, "column": 10}, "end": {"line": 50, "column": 16}}], "line": 30}, "3": {"loc": {"start": {"line": 53, "column": 9}, "end": {"line": 66, "column": 12}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 9}, "end": {"line": 53, "column": 29}}, {"start": {"line": 54, "column": 11}, "end": {"line": 66, "column": 11}}], "line": 53}, "4": {"loc": {"start": {"line": 54, "column": 11}, "end": {"line": 66, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 55, "column": 12}, "end": {"line": 59, "column": 14}}, {"start": {"line": 61, "column": 12}, "end": {"line": 65, "column": 14}}], "line": 54}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardSelectGroup.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardSelectGroup.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 79}, "end": {"line": 63, "column": 1}}, "1": {"start": {"line": 23, "column": 34}, "end": {"line": 28, "column": 5}}, "2": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 49}}, "3": {"start": {"line": 30, "column": 4}, "end": {"line": 33, "column": 5}}, "4": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 68}}, "5": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 18}}, "6": {"start": {"line": 34, "column": 26}, "end": {"line": 45, "column": 5}}, "7": {"start": {"line": 35, "column": 6}, "end": {"line": 44, "column": 7}}, "8": {"start": {"line": 37, "column": 10}, "end": {"line": 37, "column": 38}}, "9": {"start": {"line": 39, "column": 10}, "end": {"line": 39, "column": 39}}, "10": {"start": {"line": 41, "column": 10}, "end": {"line": 41, "column": 38}}, "11": {"start": {"line": 43, "column": 10}, "end": {"line": 43, "column": 20}}, "12": {"start": {"line": 47, "column": 4}, "end": {"line": 61, "column": 6}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 3}}, "loc": {"start": {"line": 21, "column": 8}, "end": {"line": 62, "column": 3}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 7}}, "loc": {"start": {"line": 24, "column": 42}, "end": {"line": 26, "column": 7}}, "line": 24}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 34, "column": 26}, "end": {"line": 34, "column": 27}}, "loc": {"start": {"line": 34, "column": 57}, "end": {"line": 45, "column": 5}}, "line": 34}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 33, "column": 5}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 33, "column": 5}}, {"start": {}, "end": {}}], "line": 30}, "1": {"loc": {"start": {"line": 35, "column": 6}, "end": {"line": 44, "column": 7}}, "type": "switch", "locations": [{"start": {"line": 36, "column": 8}, "end": {"line": 37, "column": 38}}, {"start": {"line": 38, "column": 8}, "end": {"line": 39, "column": 39}}, {"start": {"line": 40, "column": 8}, "end": {"line": 41, "column": 38}}, {"start": {"line": 42, "column": 8}, "end": {"line": 43, "column": 20}}], "line": 35}, "2": {"loc": {"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": 29}}, {"start": {"line": 56, "column": 33}, "end": {"line": 56, "column": 40}}], "line": 56}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0, 0, 0], "2": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardTable.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardTable.tsx", "statementMap": {"0": {"start": {"line": 33, "column": 32}, "end": {"line": 33, "column": 58}}, "1": {"start": {"line": 36, "column": 24}, "end": {"line": 69, "column": 24}}, "2": {"start": {"line": 37, "column": 4}, "end": {"line": 39, "column": 5}}, "3": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 21}}, "4": {"start": {"line": 41, "column": 23}, "end": {"line": 41, "column": 35}}, "5": {"start": {"line": 42, "column": 25}, "end": {"line": 42, "column": 35}}, "6": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 92}}, "7": {"start": {"line": 45, "column": 43}, "end": {"line": 45, "column": 91}}, "8": {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, "9": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 21}}, "10": {"start": {"line": 51, "column": 4}, "end": {"line": 66, "column": 7}}, "11": {"start": {"line": 53, "column": 26}, "end": {"line": 53, "column": 57}}, "12": {"start": {"line": 54, "column": 17}, "end": {"line": 54, "column": 31}}, "13": {"start": {"line": 55, "column": 17}, "end": {"line": 55, "column": 31}}, "14": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 33}}, "15": {"start": {"line": 58, "column": 24}, "end": {"line": 58, "column": 33}}, "16": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 34}}, "17": {"start": {"line": 59, "column": 24}, "end": {"line": 59, "column": 34}}, "18": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 44}}, "19": {"start": {"line": 62, "column": 23}, "end": {"line": 62, "column": 44}}, "20": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 44}}, "21": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 44}}, "22": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 15}}, "23": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 22}}, "24": {"start": {"line": 71, "column": 2}, "end": {"line": 85, "column": 4}}}, "fnMap": {"0": {"name": "CardTable", "decl": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 33}}, "loc": {"start": {"line": 30, "column": 50}, "end": {"line": 86, "column": 1}}, "line": 30}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 32}, "end": {"line": 36, "column": 33}}, "loc": {"start": {"line": 36, "column": 38}, "end": {"line": 69, "column": 3}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 36}, "end": {"line": 45, "column": 37}}, "loc": {"start": {"line": 45, "column": 43}, "end": {"line": 45, "column": 91}}, "line": 45}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 51, "column": 20}, "end": {"line": 51, "column": 21}}, "loc": {"start": {"line": 51, "column": 30}, "end": {"line": 66, "column": 5}}, "line": 51}}, "branchMap": {"0": {"loc": {"start": {"line": 37, "column": 4}, "end": {"line": 39, "column": 5}}, "type": "if", "locations": [{"start": {"line": 37, "column": 4}, "end": {"line": 39, "column": 5}}, {"start": {}, "end": {}}], "line": 37}, "1": {"loc": {"start": {"line": 45, "column": 43}, "end": {"line": 45, "column": 91}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 43}, "end": {"line": 45, "column": 56}}, {"start": {"line": 45, "column": 60}, "end": {"line": 45, "column": 91}}], "line": 45}, "2": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, {"start": {}, "end": {}}], "line": 47}, "3": {"loc": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 19}}, {"start": {"line": 47, "column": 23}, "end": {"line": 47, "column": 57}}], "line": 47}, "4": {"loc": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 33}}, "type": "if", "locations": [{"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 33}}, {"start": {}, "end": {}}], "line": 58}, "5": {"loc": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 34}}, "type": "if", "locations": [{"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 34}}, {"start": {}, "end": {}}], "line": 59}, "6": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 44}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 44}}, {"start": {}, "end": {}}], "line": 62}, "7": {"loc": {"start": {"line": 62, "column": 30}, "end": {"line": 62, "column": 43}}, "type": "cond-expr", "locations": [{"start": {"line": 62, "column": 37}, "end": {"line": 62, "column": 38}}, {"start": {"line": 62, "column": 41}, "end": {"line": 62, "column": 43}}], "line": 62}, "8": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 44}}, "type": "if", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 44}}, {"start": {}, "end": {}}], "line": 63}, "9": {"loc": {"start": {"line": 63, "column": 30}, "end": {"line": 63, "column": 43}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 37}, "end": {"line": 63, "column": 39}}, {"start": {"line": 63, "column": 42}, "end": {"line": 63, "column": 43}}], "line": 63}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardTabs.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/CardTabs.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 51}, "end": {"line": 40, "column": 1}}, "1": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 46}}, "2": {"start": {"line": 19, "column": 34}, "end": {"line": 19, "column": 46}}, "3": {"start": {"line": 21, "column": 22}, "end": {"line": 24, "column": 29}}, "4": {"start": {"line": 22, "column": 22}, "end": {"line": 22, "column": 53}}, "5": {"start": {"line": 22, "column": 41}, "end": {"line": 22, "column": 52}}, "6": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 63}}, "7": {"start": {"line": 26, "column": 2}, "end": {"line": 39, "column": 4}}, "8": {"start": {"line": 29, "column": 8}, "end": {"line": 36, "column": 17}}, "9": {"start": {"line": 31, "column": 25}, "end": {"line": 31, "column": 41}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 51}, "end": {"line": 13, "column": 52}}, "loc": {"start": {"line": 18, "column": 6}, "end": {"line": 40, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 30}, "end": {"line": 21, "column": 31}}, "loc": {"start": {"line": 21, "column": 36}, "end": {"line": 24, "column": 3}}, "line": 21}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 22, "column": 34}, "end": {"line": 22, "column": 35}}, "loc": {"start": {"line": 22, "column": 41}, "end": {"line": 22, "column": 52}}, "line": 22}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 28, "column": 23}, "end": {"line": 28, "column": 24}}, "loc": {"start": {"line": 29, "column": 8}, "end": {"line": 36, "column": 17}}, "line": 29}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 31, "column": 19}, "end": {"line": 31, "column": 20}}, "loc": {"start": {"line": 31, "column": 25}, "end": {"line": 31, "column": 41}}, "line": 31}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 46}}, "type": "if", "locations": [{"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 46}}, {"start": {}, "end": {}}], "line": 19}, "1": {"loc": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 11}}, {"start": {"line": 19, "column": 15}, "end": {"line": 19, "column": 32}}], "line": 19}, "2": {"loc": {"start": {"line": 23, "column": 11}, "end": {"line": 23, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 23, "column": 29}, "end": {"line": 23, "column": 50}}, {"start": {"line": 23, "column": 53}, "end": {"line": 23, "column": 62}}], "line": 23}, "3": {"loc": {"start": {"line": 35, "column": 11}, "end": {"line": 35, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 11}, "end": {"line": 35, "column": 28}}, {"start": {"line": 35, "column": 32}, "end": {"line": 35, "column": 76}}], "line": 35}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/InfiniteScrollTable.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/InfiniteScrollTable.tsx", "statementMap": {"0": {"start": {"line": 21, "column": 23}, "end": {"line": 29, "column": 1}}, "1": {"start": {"line": 22, "column": 2}, "end": {"line": 24, "column": 3}}, "2": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 30}}, "3": {"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, "4": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 32}}, "5": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 26}}, "6": {"start": {"line": 60, "column": 28}, "end": {"line": 60, "column": 56}}, "7": {"start": {"line": 61, "column": 34}, "end": {"line": 61, "column": 61}}, "8": {"start": {"line": 63, "column": 15}, "end": {"line": 63, "column": 56}}, "9": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 40}}, "10": {"start": {"line": 65, "column": 28}, "end": {"line": 73, "column": 3}}, "11": {"start": {"line": 66, "column": 4}, "end": {"line": 72, "column": 9}}, "12": {"start": {"line": 68, "column": 8}, "end": {"line": 70, "column": 13}}, "13": {"start": {"line": 75, "column": 30}, "end": {"line": 82, "column": 3}}, "14": {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 79}}, "15": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 47}}, "16": {"start": {"line": 84, "column": 16}, "end": {"line": 100, "column": 4}}, "17": {"start": {"line": 102, "column": 35}, "end": {"line": 122, "column": 3}}, "18": {"start": {"line": 104, "column": 6}, "end": {"line": 119, "column": 7}}, "19": {"start": {"line": 105, "column": 58}, "end": {"line": 105, "column": 77}}, "20": {"start": {"line": 107, "column": 8}, "end": {"line": 118, "column": 9}}, "21": {"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 57}}, "22": {"start": {"line": 111, "column": 10}, "end": {"line": 117, "column": 11}}, "23": {"start": {"line": 116, "column": 12}, "end": {"line": 116, "column": 28}}, "24": {"start": {"line": 124, "column": 2}, "end": {"line": 132, "column": 66}}, "25": {"start": {"line": 125, "column": 18}, "end": {"line": 129, "column": 11}}, "26": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "27": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 60}}, "28": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 37}}, "29": {"start": {"line": 131, "column": 17}, "end": {"line": 131, "column": 36}}, "30": {"start": {"line": 134, "column": 23}, "end": {"line": 217, "column": 4}}, "31": {"start": {"line": 135, "column": 4}, "end": {"line": 140, "column": 5}}, "32": {"start": {"line": 136, "column": 27}, "end": {"line": 138, "column": 7}}, "33": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 41}}, "34": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 43}}, "35": {"start": {"line": 143, "column": 4}, "end": {"line": 152, "column": 5}}, "36": {"start": {"line": 144, "column": 6}, "end": {"line": 151, "column": 8}}, "37": {"start": {"line": 154, "column": 4}, "end": {"line": 210, "column": 6}}, "38": {"start": {"line": 157, "column": 10}, "end": {"line": 200, "column": 15}}, "39": {"start": {"line": 168, "column": 52}, "end": {"line": 169, "column": 55}}, "40": {"start": {"line": 170, "column": 36}, "end": {"line": 174, "column": 16}}, "41": {"start": {"line": 176, "column": 35}, "end": {"line": 176, "column": 50}}, "42": {"start": {"line": 177, "column": 38}, "end": {"line": 177, "column": 136}}, "43": {"start": {"line": 179, "column": 34}, "end": {"line": 186, "column": 75}}, "44": {"start": {"line": 188, "column": 14}, "end": {"line": 198, "column": 16}}, "45": {"start": {"line": 193, "column": 20}, "end": {"line": 193, "column": 77}}, "46": {"start": {"line": 219, "column": 23}, "end": {"line": 219, "column": 74}}, "47": {"start": {"line": 221, "column": 2}, "end": {"line": 297, "column": 4}}, "48": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 50}}, "49": {"start": {"line": 239, "column": 16}, "end": {"line": 241, "column": 21}}, "50": {"start": {"line": 249, "column": 16}, "end": {"line": 289, "column": 21}}, "51": {"start": {"line": 251, "column": 71}, "end": {"line": 252, "column": 78}}, "52": {"start": {"line": 253, "column": 44}, "end": {"line": 258, "column": 22}}, "53": {"start": {"line": 259, "column": 20}, "end": {"line": 287, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 23}, "end": {"line": 21, "column": 24}}, "loc": {"start": {"line": 21, "column": 53}, "end": {"line": 29, "column": 1}}, "line": 21}, "1": {"name": "InfiniteScrollTable", "decl": {"start": {"line": 49, "column": 9}, "end": {"line": 49, "column": 28}}, "loc": {"start": {"line": 59, "column": 42}, "end": {"line": 298, "column": 1}}, "line": 59}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 24}}, "loc": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 40}}, "line": 63}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 65, "column": 28}, "end": {"line": 65, "column": 29}}, "loc": {"start": {"line": 66, "column": 4}, "end": {"line": 72, "column": 9}}, "line": 66}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 67, "column": 19}, "end": {"line": 67, "column": 20}}, "loc": {"start": {"line": 68, "column": 8}, "end": {"line": 70, "column": 13}}, "line": 68}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 5}}, "loc": {"start": {"line": 76, "column": 22}, "end": {"line": 80, "column": 5}}, "line": 76}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 5}}, "loc": {"start": {"line": 103, "column": 53}, "end": {"line": 120, "column": 5}}, "line": 103}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 124, "column": 12}, "end": {"line": 124, "column": 13}}, "loc": {"start": {"line": 124, "column": 18}, "end": {"line": 132, "column": 3}}, "line": 124}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 125, "column": 29}, "end": {"line": 125, "column": 30}}, "loc": {"start": {"line": 125, "column": 35}, "end": {"line": 129, "column": 5}}, "line": 125}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 131, "column": 11}, "end": {"line": 131, "column": 12}}, "loc": {"start": {"line": 131, "column": 17}, "end": {"line": 131, "column": 36}}, "line": 131}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 134, "column": 31}, "end": {"line": 134, "column": 32}}, "loc": {"start": {"line": 134, "column": 37}, "end": {"line": 211, "column": 3}}, "line": 134}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 136, "column": 54}, "end": {"line": 136, "column": 55}}, "loc": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 41}}, "line": 137}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 156, "column": 38}, "end": {"line": 156, "column": 39}}, "loc": {"start": {"line": 157, "column": 10}, "end": {"line": 200, "column": 15}}, "line": 157}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 167, "column": 39}, "end": {"line": 167, "column": 40}}, "loc": {"start": {"line": 167, "column": 49}, "end": {"line": 199, "column": 13}}, "line": 167}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 192, "column": 27}, "end": {"line": 192, "column": 28}}, "loc": {"start": {"line": 193, "column": 20}, "end": {"line": 193, "column": 77}}, "line": 193}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 224, "column": 16}, "end": {"line": 224, "column": 17}}, "loc": {"start": {"line": 224, "column": 23}, "end": {"line": 226, "column": 7}}, "line": 224}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 238, "column": 27}, "end": {"line": 238, "column": 28}}, "loc": {"start": {"line": 239, "column": 16}, "end": {"line": 241, "column": 21}}, "line": 239}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 248, "column": 43}, "end": {"line": 248, "column": 44}}, "loc": {"start": {"line": 249, "column": 16}, "end": {"line": 289, "column": 21}}, "line": 249}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 250, "column": 43}, "end": {"line": 250, "column": 44}}, "loc": {"start": {"line": 250, "column": 55}, "end": {"line": 288, "column": 19}}, "line": 250}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 24, "column": 3}}, "type": "if", "locations": [{"start": {"line": 22, "column": 2}, "end": {"line": 24, "column": 3}}, {"start": {}, "end": {}}], "line": 22}, "1": {"loc": {"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, "type": "if", "locations": [{"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, {"start": {}, "end": {}}], "line": 25}, "2": {"loc": {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 40}, "end": {"line": 78, "column": 69}}, {"start": {"line": 78, "column": 72}, "end": {"line": 78, "column": 79}}], "line": 78}, "3": {"loc": {"start": {"line": 104, "column": 6}, "end": {"line": 119, "column": 7}}, "type": "if", "locations": [{"start": {"line": 104, "column": 6}, "end": {"line": 119, "column": 7}}, {"start": {}, "end": {}}], "line": 104}, "4": {"loc": {"start": {"line": 107, "column": 8}, "end": {"line": 118, "column": 9}}, "type": "if", "locations": [{"start": {"line": 107, "column": 8}, "end": {"line": 118, "column": 9}}, {"start": {}, "end": {}}], "line": 107}, "5": {"loc": {"start": {"line": 111, "column": 10}, "end": {"line": 117, "column": 11}}, "type": "if", "locations": [{"start": {"line": 111, "column": 10}, "end": {"line": 117, "column": 11}}, {"start": {}, "end": {}}], "line": 111}, "6": {"loc": {"start": {"line": 112, "column": 12}, "end": {"line": 114, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 28}}, {"start": {"line": 113, "column": 12}, "end": {"line": 113, "column": 31}}, {"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 51}}], "line": 112}, "7": {"loc": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "type": "if", "locations": [{"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, {"start": {}, "end": {}}], "line": 126}, "8": {"loc": {"start": {"line": 135, "column": 4}, "end": {"line": 140, "column": 5}}, "type": "if", "locations": [{"start": {"line": 135, "column": 4}, "end": {"line": 140, "column": 5}}, {"start": {}, "end": {}}], "line": 135}, "9": {"loc": {"start": {"line": 143, "column": 4}, "end": {"line": 152, "column": 5}}, "type": "if", "locations": [{"start": {"line": 143, "column": 4}, "end": {"line": 152, "column": 5}}, {"start": {}, "end": {}}], "line": 143}, "10": {"loc": {"start": {"line": 168, "column": 32}, "end": {"line": 168, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 168, "column": 45}, "end": {"line": 168, "column": 47}}], "line": 168}, "11": {"loc": {"start": {"line": 168, "column": 53}, "end": {"line": 169, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 168, "column": 53}, "end": {"line": 169, "column": 21}}, {"start": {"line": 169, "column": 25}, "end": {"line": 169, "column": 27}}], "line": 168}, "12": {"loc": {"start": {"line": 171, "column": 34}, "end": {"line": 171, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 34}, "end": {"line": 171, "column": 42}}, {"start": {"line": 171, "column": 46}, "end": {"line": 171, "column": 67}}], "line": 171}, "13": {"loc": {"start": {"line": 172, "column": 35}, "end": {"line": 172, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 172, "column": 35}, "end": {"line": 172, "column": 43}}, {"start": {"line": 172, "column": 47}, "end": {"line": 172, "column": 69}}], "line": 172}, "14": {"loc": {"start": {"line": 177, "column": 38}, "end": {"line": 177, "column": 136}}, "type": "binary-expr", "locations": [{"start": {"line": 177, "column": 38}, "end": {"line": 177, "column": 72}}, {"start": {"line": 177, "column": 76}, "end": {"line": 177, "column": 108}}, {"start": {"line": 177, "column": 112}, "end": {"line": 177, "column": 136}}], "line": 177}, "15": {"loc": {"start": {"line": 179, "column": 34}, "end": {"line": 186, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 182, "column": 18}, "end": {"line": 184, "column": 25}}, {"start": {"line": 186, "column": 18}, "end": {"line": 186, "column": 75}}], "line": 179}, "16": {"loc": {"start": {"line": 193, "column": 20}, "end": {"line": 193, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 193, "column": 20}, "end": {"line": 193, "column": 47}}, {"start": {"line": 193, "column": 51}, "end": {"line": 193, "column": 77}}], "line": 193}, "17": {"loc": {"start": {"line": 202, "column": 9}, "end": {"line": 208, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 202, "column": 9}, "end": {"line": 202, "column": 27}}, {"start": {"line": 203, "column": 10}, "end": {"line": 207, "column": 15}}], "line": 202}, "18": {"loc": {"start": {"line": 219, "column": 23}, "end": {"line": 219, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 219, "column": 23}, "end": {"line": 219, "column": 33}}, {"start": {"line": 219, "column": 37}, "end": {"line": 219, "column": 74}}], "line": 219}, "19": {"loc": {"start": {"line": 235, "column": 9}, "end": {"line": 293, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 236, "column": 10}, "end": {"line": 244, "column": 18}}, {"start": {"line": 246, "column": 10}, "end": {"line": 292, "column": 11}}], "line": 235}, "20": {"loc": {"start": {"line": 246, "column": 10}, "end": {"line": 292, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 246, "column": 10}, "end": {"line": 246, "column": 23}}, {"start": {"line": 247, "column": 12}, "end": {"line": 291, "column": 20}}], "line": 246}, "21": {"loc": {"start": {"line": 251, "column": 38}, "end": {"line": 251, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 251, "column": 51}, "end": {"line": 251, "column": 53}}], "line": 251}, "22": {"loc": {"start": {"line": 251, "column": 72}, "end": {"line": 252, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 251, "column": 72}, "end": {"line": 252, "column": 44}}, {"start": {"line": 252, "column": 48}, "end": {"line": 252, "column": 50}}], "line": 251}, "23": {"loc": {"start": {"line": 254, "column": 40}, "end": {"line": 254, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 254, "column": 40}, "end": {"line": 254, "column": 48}}, {"start": {"line": 254, "column": 52}, "end": {"line": 254, "column": 73}}], "line": 254}, "24": {"loc": {"start": {"line": 255, "column": 41}, "end": {"line": 255, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 255, "column": 41}, "end": {"line": 255, "column": 49}}, {"start": {"line": 255, "column": 53}, "end": {"line": 255, "column": 75}}], "line": 255}, "25": {"loc": {"start": {"line": 271, "column": 47}, "end": {"line": 271, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 271, "column": 47}, "end": {"line": 271, "column": 58}}, {"start": {"line": 271, "column": 62}, "end": {"line": 271, "column": 69}}], "line": 271}, "26": {"loc": {"start": {"line": 278, "column": 27}, "end": {"line": 284, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 278, "column": 27}, "end": {"line": 278, "column": 53}}, {"start": {"line": 279, "column": 28}, "end": {"line": 283, "column": 35}}], "line": 278}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/ModuleModal.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/ModuleModal.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 55}, "end": {"line": 34, "column": 1}}, "1": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 27}}, "2": {"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": 27}}, "3": {"start": {"line": 20, "column": 29}, "end": {"line": 22, "column": 3}}, "4": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 48}}, "5": {"start": {"line": 21, "column": 38}, "end": {"line": 21, "column": 48}}, "6": {"start": {"line": 24, "column": 2}, "end": {"line": 33, "column": 4}}, "7": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 43}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 55}, "end": {"line": 12, "column": 56}}, "loc": {"start": {"line": 17, "column": 6}, "end": {"line": 34, "column": 1}}, "line": 17}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 29}, "end": {"line": 20, "column": 30}}, "loc": {"start": {"line": 20, "column": 70}, "end": {"line": 22, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 17}, "end": {"line": 28, "column": 18}}, "loc": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 43}}, "line": 28}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 27}}, "type": "if", "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 27}}, {"start": {}, "end": {}}], "line": 18}, "1": {"loc": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 48}}, "type": "if", "locations": [{"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 48}}, {"start": {}, "end": {}}], "line": 21}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/Spinner.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/Spinner.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 40}, "end": {"line": 15, "column": 1}}, "1": {"start": {"line": 10, "column": 2}, "end": {"line": 14, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 40}, "end": {"line": 7, "column": 41}}, "loc": {"start": {"line": 9, "column": 6}, "end": {"line": 15, "column": 1}}, "line": 9}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 39}}, "type": "default-arg", "locations": [{"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 39}}], "line": 8}}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {"0": [0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/svgIcons.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/iCard/svgIcons.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 27}, "end": {"line": 19, "column": 1}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 18, "column": 4}}, "2": {"start": {"line": 21, "column": 32}, "end": {"line": 35, "column": 1}}, "3": {"start": {"line": 22, "column": 2}, "end": {"line": 34, "column": 8}}, "4": {"start": {"line": 37, "column": 24}, "end": {"line": 55, "column": 1}}, "5": {"start": {"line": 38, "column": 2}, "end": {"line": 54, "column": 8}}, "6": {"start": {"line": 57, "column": 29}, "end": {"line": 82, "column": 1}}, "7": {"start": {"line": 58, "column": 2}, "end": {"line": 81, "column": 4}}, "8": {"start": {"line": 84, "column": 31}, "end": {"line": 104, "column": 1}}, "9": {"start": {"line": 85, "column": 2}, "end": {"line": 103, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 28}}, "loc": {"start": {"line": 3, "column": 71}, "end": {"line": 19, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 32}, "end": {"line": 21, "column": 33}}, "loc": {"start": {"line": 22, "column": 2}, "end": {"line": 34, "column": 8}}, "line": 22}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 37, "column": 24}, "end": {"line": 37, "column": 25}}, "loc": {"start": {"line": 38, "column": 2}, "end": {"line": 54, "column": 8}}, "line": 38}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 57, "column": 29}, "end": {"line": 57, "column": 30}}, "loc": {"start": {"line": 57, "column": 73}, "end": {"line": 82, "column": 1}}, "line": 57}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 84, "column": 31}, "end": {"line": 84, "column": 32}}, "loc": {"start": {"line": 84, "column": 75}, "end": {"line": 104, "column": 1}}, "line": 84}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/utils/textConstants.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DashboardWidget/utils/textConstants.ts", "statementMap": {"0": {"start": {"line": 1, "column": 36}, "end": {"line": 4, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DropdownSearch/index.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/DropdownSearch/index.tsx", "statementMap": {"0": {"start": {"line": 22, "column": 25}, "end": {"line": 28, "column": 1}}, "1": {"start": {"line": 23, "column": 19}, "end": {"line": 26, "column": 6}}, "2": {"start": {"line": 24, "column": 28}, "end": {"line": 24, "column": 61}}, "3": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 112}}, "4": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 18}}, "5": {"start": {"line": 30, "column": 30}, "end": {"line": 69, "column": 1}}, "6": {"start": {"line": 32, "column": 29}, "end": {"line": 46, "column": 9}}, "7": {"start": {"line": 33, "column": 12}, "end": {"line": 45, "column": 13}}, "8": {"start": {"line": 34, "column": 16}, "end": {"line": 42, "column": 17}}, "9": {"start": {"line": 35, "column": 20}, "end": {"line": 38, "column": 24}}, "10": {"start": {"line": 36, "column": 44}, "end": {"line": 36, "column": 76}}, "11": {"start": {"line": 37, "column": 24}, "end": {"line": 37, "column": 46}}, "12": {"start": {"line": 40, "column": 40}, "end": {"line": 40, "column": 78}}, "13": {"start": {"line": 41, "column": 20}, "end": {"line": 41, "column": 45}}, "14": {"start": {"line": 44, "column": 16}, "end": {"line": 44, "column": 31}}, "15": {"start": {"line": 47, "column": 25}, "end": {"line": 47, "column": 91}}, "16": {"start": {"line": 47, "column": 39}, "end": {"line": 47, "column": 72}}, "17": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 36}}, "18": {"start": {"line": 49, "column": 8}, "end": {"line": 67, "column": 10}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 22, "column": 25}, "end": {"line": 22, "column": 26}}, "loc": {"start": {"line": 22, "column": 83}, "end": {"line": 28, "column": 1}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 34}, "end": {"line": 23, "column": 35}}, "loc": {"start": {"line": 23, "column": 46}, "end": {"line": 26, "column": 5}}, "line": 23}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 5}}, "loc": {"start": {"line": 31, "column": 131}, "end": {"line": 68, "column": 5}}, "line": 31}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 32, "column": 29}, "end": {"line": 32, "column": 30}}, "loc": {"start": {"line": 32, "column": 53}, "end": {"line": 46, "column": 9}}, "line": 32}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 35, "column": 42}, "end": {"line": 35, "column": 43}}, "loc": {"start": {"line": 35, "column": 53}, "end": {"line": 38, "column": 21}}, "line": 35}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 47, "column": 33}, "end": {"line": 47, "column": 34}}, "loc": {"start": {"line": 47, "column": 39}, "end": {"line": 47, "column": 72}}, "line": 47}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 15}, "end": {"line": 25, "column": 111}}, "type": "cond-expr", "locations": [{"start": {"line": 25, "column": 40}, "end": {"line": 25, "column": 82}}, {"start": {"line": 25, "column": 86}, "end": {"line": 25, "column": 110}}], "line": 25}, "1": {"loc": {"start": {"line": 31, "column": 72}, "end": {"line": 31, "column": 88}}, "type": "default-arg", "locations": [{"start": {"line": 31, "column": 83}, "end": {"line": 31, "column": 88}}], "line": 31}, "2": {"loc": {"start": {"line": 33, "column": 12}, "end": {"line": 45, "column": 13}}, "type": "if", "locations": [{"start": {"line": 33, "column": 12}, "end": {"line": 45, "column": 13}}, {"start": {"line": 43, "column": 19}, "end": {"line": 45, "column": 13}}], "line": 33}, "3": {"loc": {"start": {"line": 34, "column": 16}, "end": {"line": 42, "column": 17}}, "type": "if", "locations": [{"start": {"line": 34, "column": 16}, "end": {"line": 42, "column": 17}}, {"start": {"line": 39, "column": 23}, "end": {"line": 42, "column": 17}}], "line": 34}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/SendEmailModal/index.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/SendEmailModal/index.tsx", "statementMap": {"0": {"start": {"line": 25, "column": 25}, "end": {"line": 30, "column": 2}}, "1": {"start": {"line": 32, "column": 23}, "end": {"line": 175, "column": 1}}, "2": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 63}}, "3": {"start": {"line": 41, "column": 34}, "end": {"line": 41, "column": 57}}, "4": {"start": {"line": 42, "column": 38}, "end": {"line": 42, "column": 62}}, "5": {"start": {"line": 51, "column": 8}, "end": {"line": 54, "column": 6}}, "6": {"start": {"line": 55, "column": 23}, "end": {"line": 55, "column": 79}}, "7": {"start": {"line": 57, "column": 4}, "end": {"line": 62, "column": 31}}, "8": {"start": {"line": 58, "column": 8}, "end": {"line": 61, "column": 9}}, "9": {"start": {"line": 59, "column": 28}, "end": {"line": 59, "column": 60}}, "10": {"start": {"line": 60, "column": 12}, "end": {"line": 60, "column": 41}}, "11": {"start": {"line": 64, "column": 28}, "end": {"line": 73, "column": 5}}, "12": {"start": {"line": 65, "column": 8}, "end": {"line": 72, "column": 9}}, "13": {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 31}}, "14": {"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 36}}, "15": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 57}}, "16": {"start": {"line": 71, "column": 12}, "end": {"line": 71, "column": 32}}, "17": {"start": {"line": 74, "column": 4}, "end": {"line": 174, "column": 6}}, "18": {"start": {"line": 106, "column": 40}, "end": {"line": 114, "column": 42}}, "19": {"start": {"line": 127, "column": 40}, "end": {"line": 135, "column": 42}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 32, "column": 23}, "end": {"line": 32, "column": 24}}, "loc": {"start": {"line": 39, "column": 13}, "end": {"line": 175, "column": 1}}, "line": 39}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 57, "column": 14}, "end": {"line": 57, "column": 15}}, "loc": {"start": {"line": 57, "column": 20}, "end": {"line": 62, "column": 5}}, "line": 57}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 64, "column": 28}, "end": {"line": 64, "column": 29}}, "loc": {"start": {"line": 64, "column": 64}, "end": {"line": 73, "column": 5}}, "line": 64}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 105, "column": 44}, "end": {"line": 105, "column": 45}}, "loc": {"start": {"line": 106, "column": 40}, "end": {"line": 114, "column": 42}}, "line": 106}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 126, "column": 44}, "end": {"line": 126, "column": 45}}, "loc": {"start": {"line": 127, "column": 40}, "end": {"line": 135, "column": 42}}, "line": 127}}, "branchMap": {"0": {"loc": {"start": {"line": 55, "column": 23}, "end": {"line": 55, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 23}, "end": {"line": 55, "column": 73}}, {"start": {"line": 55, "column": 77}, "end": {"line": 55, "column": 79}}], "line": 55}, "1": {"loc": {"start": {"line": 58, "column": 8}, "end": {"line": 61, "column": 9}}, "type": "if", "locations": [{"start": {"line": 58, "column": 8}, "end": {"line": 61, "column": 9}}, {"start": {}, "end": {}}], "line": 58}, "2": {"loc": {"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": 22}}, {"start": {"line": 58, "column": 26}, "end": {"line": 58, "column": 47}}], "line": 58}, "3": {"loc": {"start": {"line": 169, "column": 25}, "end": {"line": 169, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 169, "column": 37}, "end": {"line": 169, "column": 91}}, {"start": {"line": 169, "column": 94}, "end": {"line": 169, "column": 100}}], "line": 169}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/SendEmailModal/useYupValidationResolver.ts": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/SendEmailModal/useYupValidationResolver.ts", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 35, "column": 4}}, "1": {"start": {"line": 6, "column": 6}, "end": {"line": 32, "column": 7}}, "2": {"start": {"line": 7, "column": 23}, "end": {"line": 9, "column": 10}}, "3": {"start": {"line": 11, "column": 8}, "end": {"line": 14, "column": 10}}, "4": {"start": {"line": 16, "column": 8}, "end": {"line": 31, "column": 10}}, "5": {"start": {"line": 22, "column": 18}, "end": {"line": 28, "column": 13}}}, "fnMap": {"0": {"name": "useYupValidationResolver", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 33}}, "loc": {"start": {"line": 3, "column": 47}, "end": {"line": 36, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 5, "column": 4}, "end": {"line": 5, "column": 5}}, "loc": {"start": {"line": 5, "column": 20}, "end": {"line": 33, "column": 5}}, "line": 5}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 19, "column": 12}, "end": {"line": 19, "column": 13}}, "loc": {"start": {"line": 22, "column": 18}, "end": {"line": 28, "column": 13}}, "line": 22}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 22}, "end": {"line": 25, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 22}, "end": {"line": 25, "column": 39}}, {"start": {"line": 25, "column": 43}, "end": {"line": 25, "column": 55}}], "line": 25}}, "s": {"0": 2, "1": 2, "2": 2, "3": 1, "4": 1, "5": 2}, "f": {"0": 2, "1": 2, "2": 2}, "b": {"0": [2, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "127c6e24ca8b78a52e9f47134e9d8c67cf5aefd3"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/StatCard/StatCard.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/StatCard/StatCard.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 42}, "end": {"line": 40, "column": 1}}, "1": {"start": {"line": 14, "column": 2}, "end": {"line": 39, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 42}, "end": {"line": 13, "column": 43}}, "loc": {"start": {"line": 13, "column": 94}, "end": {"line": 40, "column": 1}}, "line": 13}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 35}, "end": {"line": 15, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 15, "column": 47}, "end": {"line": 15, "column": 56}}, {"start": {"line": 15, "column": 59}, "end": {"line": 15, "column": 61}}], "line": 15}, "1": {"loc": {"start": {"line": 18, "column": 11}, "end": {"line": 18, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 66}}, {"start": {"line": 18, "column": 69}, "end": {"line": 18, "column": 74}}], "line": 18}, "2": {"loc": {"start": {"line": 21, "column": 11}, "end": {"line": 30, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 12}, "end": {"line": 24, "column": 15}}, {"start": {"line": 26, "column": 12}, "end": {"line": 29, "column": 15}}], "line": 21}, "3": {"loc": {"start": {"line": 28, "column": 15}, "end": {"line": 28, "column": 84}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 15}, "end": {"line": 28, "column": 23}}, {"start": {"line": 28, "column": 27}, "end": {"line": 28, "column": 84}}], "line": 28}, "4": {"loc": {"start": {"line": 33, "column": 7}, "end": {"line": 37, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 7}, "end": {"line": 33, "column": 17}}, {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": 28}}, {"start": {"line": 34, "column": 8}, "end": {"line": 36, "column": 14}}], "line": 33}}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0, 0]}}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/BottomButton.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/BottomButton.tsx", "statementMap": {"0": {"start": {"line": 30, "column": 21}, "end": {"line": 50, "column": 1}}, "1": {"start": {"line": 31, "column": 2}, "end": {"line": 49, "column": 4}}, "2": {"start": {"line": 36, "column": 8}, "end": {"line": 46, "column": 17}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 30, "column": 21}, "end": {"line": 30, "column": 22}}, "loc": {"start": {"line": 30, "column": 77}, "end": {"line": 50, "column": 1}}, "line": 30}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 35, "column": 19}, "end": {"line": 35, "column": 20}}, "loc": {"start": {"line": 36, "column": 8}, "end": {"line": 46, "column": 17}}, "line": 36}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 58}, "end": {"line": 33, "column": 84}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 58}, "end": {"line": 33, "column": 78}}, {"start": {"line": 33, "column": 82}, "end": {"line": 33, "column": 84}}], "line": 33}, "1": {"loc": {"start": {"line": 38, "column": 19}, "end": {"line": 38, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 19}, "end": {"line": 38, "column": 31}}, {"start": {"line": 38, "column": 35}, "end": {"line": 38, "column": 44}}], "line": 38}, "2": {"loc": {"start": {"line": 39, "column": 46}, "end": {"line": 39, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 46}, "end": {"line": 39, "column": 62}}, {"start": {"line": 39, "column": 66}, "end": {"line": 39, "column": 68}}], "line": 39}, "3": {"loc": {"start": {"line": 42, "column": 16}, "end": {"line": 42, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 16}, "end": {"line": 42, "column": 25}}, {"start": {"line": 42, "column": 29}, "end": {"line": 42, "column": 37}}], "line": 42}}, "s": {"0": 2, "1": 48, "2": 93}, "f": {"0": 48, "1": 93}, "b": {"0": [48, 47], "1": [93, 7], "2": [93, 6], "3": [93, 93]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "305ad8dc9dd5ccf5598bec71570044e487c361ae"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/Loader.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/Loader.tsx", "statementMap": {"0": {"start": {"line": 19, "column": 15}, "end": {"line": 36, "column": 1}}, "1": {"start": {"line": 20, "column": 39}, "end": {"line": 20, "column": 44}}, "2": {"start": {"line": 22, "column": 2}, "end": {"line": 35, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 19, "column": 15}, "end": {"line": 19, "column": 16}}, "loc": {"start": {"line": 19, "column": 34}, "end": {"line": 36, "column": 1}}, "line": 19}}, "branchMap": {}, "s": {"0": 2, "1": 15, "2": 15}, "f": {"0": 15}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "812f5922d1f2d58d71d26099c29985b94722c46d"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/StepperCard.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/StepperCard.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 48}, "end": {"line": 54, "column": 1}}, "1": {"start": {"line": 18, "column": 2}, "end": {"line": 53, "column": 4}}, "2": {"start": {"line": 21, "column": 27}, "end": {"line": 21, "column": 36}}, "3": {"start": {"line": 22, "column": 28}, "end": {"line": 22, "column": 52}}, "4": {"start": {"line": 23, "column": 25}, "end": {"line": 23, "column": 51}}, "5": {"start": {"line": 25, "column": 8}, "end": {"line": 50, "column": 10}}, "6": {"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 51}}, "7": {"start": {"line": 29, "column": 31}, "end": {"line": 29, "column": 51}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 48}, "end": {"line": 13, "column": 49}}, "loc": {"start": {"line": 17, "column": 6}, "end": {"line": 54, "column": 1}}, "line": 17}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 18}}, "loc": {"start": {"line": 20, "column": 34}, "end": {"line": 51, "column": 7}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 21}, "end": {"line": 28, "column": 22}}, "loc": {"start": {"line": 28, "column": 27}, "end": {"line": 30, "column": 13}}, "line": 28}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 51}}, "type": "if", "locations": [{"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 51}}, {"start": {}, "end": {}}], "line": 29}, "1": {"loc": {"start": {"line": 42, "column": 17}, "end": {"line": 46, "column": 17}}, "type": "cond-expr", "locations": [{"start": {"line": 43, "column": 18}, "end": {"line": 43, "column": 39}}, {"start": {"line": 45, "column": 18}, "end": {"line": 45, "column": 64}}], "line": 42}}, "s": {"0": 1, "1": 38, "2": 107, "3": 107, "4": 107, "5": 107, "6": 1, "7": 1}, "f": {"0": 38, "1": 107, "2": 1}, "b": {"0": [1, 0], "1": [27, 80]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "47c187600d9908e040c21075bdbce888c7cfd494"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/index.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/index.tsx", "statementMap": {"0": {"start": {"line": 79, "column": 40}, "end": {"line": 227, "column": 1}}, "1": {"start": {"line": 100, "column": 26}, "end": {"line": 100, "column": 56}}, "2": {"start": {"line": 102, "column": 32}, "end": {"line": 102, "column": 47}}, "3": {"start": {"line": 104, "column": 18}, "end": {"line": 104, "column": 46}}, "4": {"start": {"line": 107, "column": 2}, "end": {"line": 111, "column": 24}}, "5": {"start": {"line": 108, "column": 4}, "end": {"line": 110, "column": 5}}, "6": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 63}}, "7": {"start": {"line": 116, "column": 28}, "end": {"line": 120, "column": 3}}, "8": {"start": {"line": 117, "column": 21}, "end": {"line": 117, "column": 25}}, "9": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": 21}}, "10": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 54}}, "11": {"start": {"line": 119, "column": 22}, "end": {"line": 119, "column": 54}}, "12": {"start": {"line": 128, "column": 21}, "end": {"line": 145, "column": 3}}, "13": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 21}}, "14": {"start": {"line": 130, "column": 30}, "end": {"line": 130, "column": 45}}, "15": {"start": {"line": 131, "column": 4}, "end": {"line": 137, "column": 5}}, "16": {"start": {"line": 132, "column": 22}, "end": {"line": 132, "column": 56}}, "17": {"start": {"line": 133, "column": 6}, "end": {"line": 136, "column": 7}}, "18": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 26}}, "19": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 15}}, "20": {"start": {"line": 138, "column": 4}, "end": {"line": 143, "column": 5}}, "21": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 37}}, "22": {"start": {"line": 139, "column": 18}, "end": {"line": 139, "column": 37}}, "23": {"start": {"line": 140, "column": 6}, "end": {"line": 140, "column": 40}}, "24": {"start": {"line": 141, "column": 11}, "end": {"line": 143, "column": 5}}, "25": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 55}}, "26": {"start": {"line": 142, "column": 29}, "end": {"line": 142, "column": 55}}, "27": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 22}}, "28": {"start": {"line": 147, "column": 2}, "end": {"line": 226, "column": 4}}, "29": {"start": {"line": 205, "column": 22}, "end": {"line": 205, "column": 47}}, "30": {"start": {"line": 219, "column": 14}, "end": {"line": 219, "column": 32}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 79, "column": 40}, "end": {"line": 79, "column": 41}}, "loc": {"start": {"line": 98, "column": 6}, "end": {"line": 227, "column": 1}}, "line": 98}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 107, "column": 12}, "end": {"line": 107, "column": 13}}, "loc": {"start": {"line": 107, "column": 18}, "end": {"line": 111, "column": 3}}, "line": 107}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 116, "column": 28}, "end": {"line": 116, "column": 29}}, "loc": {"start": {"line": 116, "column": 55}, "end": {"line": 120, "column": 3}}, "line": 116}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 128, "column": 21}, "end": {"line": 128, "column": 22}}, "loc": {"start": {"line": 128, "column": 33}, "end": {"line": 145, "column": 3}}, "line": 128}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 205, "column": 16}, "end": {"line": 205, "column": 17}}, "loc": {"start": {"line": 205, "column": 22}, "end": {"line": 205, "column": 47}}, "line": 205}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 217, "column": 21}, "end": {"line": 217, "column": 22}}, "loc": {"start": {"line": 217, "column": 27}, "end": {"line": 220, "column": 13}}, "line": 217}}, "branchMap": {"0": {"loc": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 83, "column": 22}, "end": {"line": 83, "column": 24}}], "line": 83}, "1": {"loc": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 30}}, "type": "default-arg", "locations": [{"start": {"line": 85, "column": 22}, "end": {"line": 85, "column": 30}}], "line": 85}, "2": {"loc": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 86, "column": 23}, "end": {"line": 86, "column": 28}}], "line": 86}, "3": {"loc": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 30}}, "type": "default-arg", "locations": [{"start": {"line": 87, "column": 25}, "end": {"line": 87, "column": 30}}], "line": 87}, "4": {"loc": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 91, "column": 14}, "end": {"line": 91, "column": 19}}], "line": 91}, "5": {"loc": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 14}}, "type": "default-arg", "locations": [{"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": 14}}], "line": 96}, "6": {"loc": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 97, "column": 15}, "end": {"line": 97, "column": 20}}], "line": 97}, "7": {"loc": {"start": {"line": 100, "column": 35}, "end": {"line": 100, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 35}, "end": {"line": 100, "column": 50}}, {"start": {"line": 100, "column": 54}, "end": {"line": 100, "column": 55}}], "line": 100}, "8": {"loc": {"start": {"line": 108, "column": 4}, "end": {"line": 110, "column": 5}}, "type": "if", "locations": [{"start": {"line": 108, "column": 4}, "end": {"line": 110, "column": 5}}, {"start": {}, "end": {}}], "line": 108}, "9": {"loc": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 54}}, "type": "if", "locations": [{"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 54}}, {"start": {}, "end": {}}], "line": 119}, "10": {"loc": {"start": {"line": 131, "column": 4}, "end": {"line": 137, "column": 5}}, "type": "if", "locations": [{"start": {"line": 131, "column": 4}, "end": {"line": 137, "column": 5}}, {"start": {}, "end": {}}], "line": 131}, "11": {"loc": {"start": {"line": 133, "column": 6}, "end": {"line": 136, "column": 7}}, "type": "if", "locations": [{"start": {"line": 133, "column": 6}, "end": {"line": 136, "column": 7}}, {"start": {}, "end": {}}], "line": 133}, "12": {"loc": {"start": {"line": 138, "column": 4}, "end": {"line": 143, "column": 5}}, "type": "if", "locations": [{"start": {"line": 138, "column": 4}, "end": {"line": 143, "column": 5}}, {"start": {"line": 141, "column": 11}, "end": {"line": 143, "column": 5}}], "line": 138}, "13": {"loc": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 37}}, "type": "if", "locations": [{"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 37}}, {"start": {}, "end": {}}], "line": 139}, "14": {"loc": {"start": {"line": 141, "column": 11}, "end": {"line": 143, "column": 5}}, "type": "if", "locations": [{"start": {"line": 141, "column": 11}, "end": {"line": 143, "column": 5}}, {"start": {}, "end": {}}], "line": 141}, "15": {"loc": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 55}}, "type": "if", "locations": [{"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 55}}, {"start": {}, "end": {}}], "line": 142}, "16": {"loc": {"start": {"line": 150, "column": 7}, "end": {"line": 150, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 7}, "end": {"line": 150, "column": 14}}, {"start": {"line": 150, "column": 18}, "end": {"line": 150, "column": 28}}, {"start": {"line": 150, "column": 32}, "end": {"line": 150, "column": 58}}], "line": 150}, "17": {"loc": {"start": {"line": 152, "column": 7}, "end": {"line": 165, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 7}, "end": {"line": 152, "column": 22}}, {"start": {"line": 153, "column": 8}, "end": {"line": 164, "column": 14}}], "line": 152}, "18": {"loc": {"start": {"line": 169, "column": 11}, "end": {"line": 182, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 11}, "end": {"line": 169, "column": 21}}, {"start": {"line": 170, "column": 12}, "end": {"line": 181, "column": 18}}], "line": 169}, "19": {"loc": {"start": {"line": 171, "column": 15}, "end": {"line": 180, "column": 15}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 15}, "end": {"line": 171, "column": 28}}, {"start": {"line": 172, "column": 16}, "end": {"line": 179, "column": 22}}], "line": 171}, "20": {"loc": {"start": {"line": 184, "column": 19}, "end": {"line": 184, "column": 37}}, "type": "cond-expr", "locations": [{"start": {"line": 184, "column": 31}, "end": {"line": 184, "column": 33}}, {"start": {"line": 184, "column": 36}, "end": {"line": 184, "column": 37}}], "line": 184}, "21": {"loc": {"start": {"line": 184, "column": 43}, "end": {"line": 184, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 184, "column": 55}, "end": {"line": 184, "column": 57}}, {"start": {"line": 184, "column": 60}, "end": {"line": 184, "column": 61}}], "line": 184}, "22": {"loc": {"start": {"line": 190, "column": 15}, "end": {"line": 190, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 190, "column": 27}, "end": {"line": 190, "column": 43}}, {"start": {"line": 190, "column": 46}, "end": {"line": 190, "column": 72}}], "line": 190}, "23": {"loc": {"start": {"line": 201, "column": 20}, "end": {"line": 201, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 201, "column": 20}, "end": {"line": 201, "column": 40}}, {"start": {"line": 201, "column": 44}, "end": {"line": 201, "column": 61}}], "line": 201}, "24": {"loc": {"start": {"line": 204, "column": 21}, "end": {"line": 206, "column": 25}}, "type": "cond-expr", "locations": [{"start": {"line": 205, "column": 16}, "end": {"line": 205, "column": 47}}, {"start": {"line": 206, "column": 16}, "end": {"line": 206, "column": 25}}], "line": 204}, "25": {"loc": {"start": {"line": 211, "column": 14}, "end": {"line": 213, "column": 33}}, "type": "cond-expr", "locations": [{"start": {"line": 212, "column": 18}, "end": {"line": 212, "column": 53}}, {"start": {"line": 213, "column": 18}, "end": {"line": 213, "column": 33}}], "line": 211}, "26": {"loc": {"start": {"line": 214, "column": 20}, "end": {"line": 214, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 214, "column": 20}, "end": {"line": 214, "column": 38}}, {"start": {"line": 214, "column": 42}, "end": {"line": 214, "column": 60}}], "line": 214}}, "s": {"0": 1, "1": 43, "2": 43, "3": 43, "4": 43, "5": 29, "6": 29, "7": 43, "8": 9, "9": 9, "10": 9, "11": 4, "12": 43, "13": 12, "14": 12, "15": 12, "16": 4, "17": 3, "18": 1, "19": 1, "20": 10, "21": 8, "22": 1, "23": 8, "24": 2, "25": 2, "26": 2, "27": 10, "28": 43, "29": 1, "30": 12}, "f": {"0": 43, "1": 29, "2": 9, "3": 12, "4": 1, "5": 12}, "b": {"0": [42], "1": [43], "2": [42], "3": [42], "4": [38], "5": [42], "6": [36], "7": [43, 39], "8": [29, 0], "9": [4, 5], "10": [4, 8], "11": [1, 2], "12": [8, 2], "13": [1, 7], "14": [2, 0], "15": [2, 0], "16": [43, 12, 3], "17": [43, 1], "18": [43, 39], "19": [39, 38], "20": [4, 39], "21": [4, 39], "22": [4, 39], "23": [43, 43], "24": [1, 42], "25": [5, 38], "26": [43, 43]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "1b04162bc7309fc32ebd4ea7d069ec1d4f3d1ffb"}, "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/svgIcons.tsx": {"path": "/Users/<USER>/Desktop/New Building/paris2-web-base/style-guide/src/components/Stepper/svgIcons.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 25}, "end": {"line": 23, "column": 1}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 22, "column": 4}}, "2": {"start": {"line": 25, "column": 33}, "end": {"line": 54, "column": 1}}, "3": {"start": {"line": 26, "column": 2}, "end": {"line": 53, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 26}}, "loc": {"start": {"line": 3, "column": 69}, "end": {"line": 23, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 33}, "end": {"line": 25, "column": 34}}, "loc": {"start": {"line": 25, "column": 77}, "end": {"line": 54, "column": 1}}, "line": 25}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 27}, "f": {"0": 1, "1": 27}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "40ced17ea2b4f20606859e5eee0e4c8a60e0974a"}}