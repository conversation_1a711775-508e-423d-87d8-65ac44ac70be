
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.23% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>92/695</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.15% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>82/428</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.05% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>27/224</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.43% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>88/655</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	</tr>

<tr>
	<td class="file high" data-value="src/components"><a href="src/components/index.html">src/components</a></td>
	<td data-value="96.66" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.66" class="pct high">96.66%</td>
	<td data-value="30" class="abs high">29/30</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="16" class="abs high">14/16</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="96.66" class="pct high">96.66%</td>
	<td data-value="30" class="abs high">29/30</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DashboardWidget/common"><a href="src/components/DashboardWidget/common/index.html">src/components/DashboardWidget/common</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="101" class="abs low">0/101</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="96" class="abs low">0/96</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DashboardWidget/common/DashboardGrid"><a href="src/components/DashboardWidget/common/DashboardGrid/index.html">src/components/DashboardWidget/common/DashboardGrid</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DashboardWidget/common/DashboardGrid/StatCard"><a href="src/components/DashboardWidget/common/DashboardGrid/StatCard/index.html">src/components/DashboardWidget/common/DashboardGrid/StatCard</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DashboardWidget/common/DashboardGrid/StatusIndicatorCard"><a href="src/components/DashboardWidget/common/DashboardGrid/StatusIndicatorCard/index.html">src/components/DashboardWidget/common/DashboardGrid/StatusIndicatorCard</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DashboardWidget/hooks"><a href="src/components/DashboardWidget/hooks/index.html">src/components/DashboardWidget/hooks</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="146" class="abs low">0/146</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="60" class="abs low">0/60</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="135" class="abs low">0/135</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DashboardWidget/iCard"><a href="src/components/DashboardWidget/iCard/index.html">src/components/DashboardWidget/iCard</a></td>
	<td data-value="2.12" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.12" class="pct low">2.12%</td>
	<td data-value="283" class="abs low">6/283</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="225" class="abs low">0/225</td>
	<td data-value="0.97" class="pct low">0.97%</td>
	<td data-value="103" class="abs low">1/103</td>
	<td data-value="2.27" class="pct low">2.27%</td>
	<td data-value="264" class="abs low">6/264</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DashboardWidget/utils"><a href="src/components/DashboardWidget/utils/index.html">src/components/DashboardWidget/utils</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/DropdownSearch"><a href="src/components/DropdownSearch/index.html">src/components/DropdownSearch</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/SendEmailModal"><a href="src/components/SendEmailModal/index.html">src/components/SendEmailModal</a></td>
	<td data-value="23.07" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 23%"></div><div class="cover-empty" style="width: 77%"></div></div>
	</td>
	<td data-value="23.07" class="pct low">23.07%</td>
	<td data-value="26" class="abs low">6/26</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="10" class="abs low">1/10</td>
	<td data-value="37.5" class="pct low">37.5%</td>
	<td data-value="8" class="abs low">3/8</td>
	<td data-value="23.07" class="pct low">23.07%</td>
	<td data-value="26" class="abs low">6/26</td>
	</tr>

<tr>
	<td class="file high" data-value="src/components/StatCard"><a href="src/components/StatCard/index.html">src/components/StatCard</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	</tr>

<tr>
	<td class="file high" data-value="src/components/Stepper"><a href="src/components/Stepper/index.html">src/components/Stepper</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="49" class="abs high">49/49</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="60" class="abs high">56/60</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="45" class="abs high">45/45</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-25T17:54:18.687Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    