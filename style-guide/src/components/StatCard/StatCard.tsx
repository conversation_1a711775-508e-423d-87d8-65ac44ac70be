import React from 'react';
import { ExternalLinkIcon } from '../DashboardWidget/iCard/svgIcons';
import './StatCard.scss';

interface StatCardProps {
  title?: string;
  count?: number;
  subTitle?: string;
  onClick?: () => void;
  isLoading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  count,
  subTitle,
  onClick,
  isLoading
}) => {
  return (
    <div className={`action-card ${isLoading ? 'loading' : ''}`}>
      <div className='card-wrapper'>
        <div className='action-card-content'>
          <div>
            <div className='action-card-title'>
              {isLoading ? <div className='skeleton skeleton-title' /> : title}
            </div>
            <div className='action-card-details'>
              {isLoading ? (
                <div className='skeleton skeleton-subtitle' />
              ) : (
                <>
                  <span className='action-card-count'>{count}</span>
                  {subTitle && (
                    <div className='action-card-description'>{subTitle}</div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
        {!isLoading && onClick && (
          <div
            className='action-card-icon'
            onClick={onClick}
            tabIndex={0}
            role='button'
          >
            <ExternalLinkIcon height={26} width={26} />
          </div>
        )}
      </div>
    </div>
  );
};

export default StatCard;
