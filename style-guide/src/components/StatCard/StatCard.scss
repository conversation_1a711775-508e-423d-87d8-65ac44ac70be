.action-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  border: 1px solid #DEE2E6;
  border-radius: 7px;
  padding: 14px 18px;
  height: 101px;
  width: 90%;
  cursor: pointer;
}

.action-card-content {
  flex-grow: 1;
}

.action-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.action-card-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-card-count {
  font-size: 23px;
  font-weight: 500;
  color: #1F4A70;
}

.action-card-description {
  font-size: 14px;
  color: #333333;
}

.action-card-icon {
  color: #1F4A70;
  margin-left: 16px;
}

/* Skeleton loader styles */
.skeleton {
  background: linear-gradient(90deg, #EDF3F7 25%, #FFFFFF 50%, #EDF3F7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-title {
  width: 90%;
  height: 25px;
  margin-bottom: 8px;
}

.skeleton-subtitle {
  width: 30%;
  height: 25px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
