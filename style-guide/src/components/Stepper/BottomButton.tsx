import React from 'react';
import {Button} from 'react-bootstrap';


export type ButtonConfig = {
  title: string;
  testID?: string;
  variant?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  customClass?: string;
};

type BottomButtonProps = {
  buttons: ButtonConfig[];
  customContainerClass?: string;
};

/**
 * The `BottomButton` component renders a container with multiple buttons based on the provided
 * configuration.
 * @param {BottomButtonProps}  - The `ButtonConfig` type defines the configuration options for a button
 * component. It includes the following properties:
 * @returns The `BottomButton` component is being returned, which renders a container with buttons
 * based on the provided `ButtonConfig` array and optional custom classes. Each button is rendered
 * using the `Button` component with properties such as variant, customClass, testID, onClick function,
 * type, and disabled state based on the corresponding values in the `ButtonConfig` object.
 */
const BottomButton = ({buttons, customContainerClass}: BottomButtonProps) => {
  return (
    <div
      className={`fixed-bottom stepper-bottom-container ${customContainerClass ?? ''}`}
    >
      {buttons.map(btn => (
        <Button
          key={`button-${btn.title}`}
          variant={btn?.variant ?? 'primary'}
          className={`stepper-bottom-button ${btn?.customClass ?? ''}`}
          data-testid={btn?.testID}
          onClick={btn?.onClick}
          type={btn?.type ?? 'button'}
          disabled={!!btn?.disabled}
        >
          {btn.title}
        </Button>
      ))}
    </div>
  );
};

export default BottomButton;
