import React from "react";
import classNames from "classnames";
import { Card, Row, Col } from "react-bootstrap";
import { CheckedCircleIcon } from "./svgIcons";
import { StepConfig } from ".";

type StepperCardProps = {
  steps: StepConfig[];
  currentStep: number;
  setStep: (step: number) => void;
};

const StepperCard: React.FC<StepperCardProps> = ({
  steps,
  currentStep,
  setStep,
}) => {
  return (
    <div className="stepper-card-container">
      {steps.map((step, index) => {
        const stepNumber = index + 1;
        const isCompleted = stepNumber < currentStep;
        const isActive = stepNumber === currentStep;

        return (
          <Card
            key={step.label}
            onClick={() => {
              if (isCompleted) setStep(stepNumber);
            }}
            className={classNames("stepper-card", {
              "stepper-card-active": isActive,
              "stepper-card-completed": isCompleted,
            })}
          >
            <Row className="stepper-card-row">
              <Col>
                <div className="stepper-card-step">Step {stepNumber}</div>
                <div className="stepper-card-lbl">{step.label}</div>
              </Col>
              <Col xs="auto">
                {isCompleted ? (
                  <CheckedCircleIcon />
                ) : (
                  <div className="stepper-card-unchecked-crl" />
                )}
              </Col>
            </Row>
          </Card>
        );
      })}
    </div>
  );
};

export default StepperCard;
