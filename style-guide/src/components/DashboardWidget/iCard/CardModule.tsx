import React, { useMemo, useState, useCallback } from "react";
import { RotateCw } from "lucide-react";
import classNames from "classnames";
import {
  Vessel,
  MultiSelectConfig,
  GridComponentType,
  ChartData,
} from "../types/card-types";
import CardTable from "./CardTable";
import CardGrid from "./CardGrid";
import { CardModuleHeader } from "./CardModuleHeader";
import { CardDropdownSelectors } from "./CardDropdownSelectors";
import { ModuleModal } from "./ModuleModal";
import { CardTabs } from "./CardTabs";
import "./styles/CardContainer.scss";
import { ColumnDef } from "@tanstack/react-table";
export interface CardModuleProps {
  readonly title: string;
  readonly vessels: Vessel[];
  readonly multiVesselSelects: MultiSelectConfig[];

  readonly staticData: {
    readonly tabs: string[];
    readonly tableHeaders: string[];
    readonly badgeColors: string[];
    readonly chartData: ChartData; //it may change
     barChartMaxRange: number;
    readonly configKey? : string;
  };

  // Callbacks
  readonly onRefresh: () => void;
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
  readonly fetchNextPage: () => void;
  readonly onChangeActiveTab: (activeTab: string) => void;
  
  readonly sizeKey: "sm" | "md" | "lg";

  // State flags
  readonly visibleConfig: {
    readonly IsiconRenderVisible?: boolean;
    readonly IsenLargeIconVisible?: boolean;
    readonly IsVesselSelectVisible?: boolean;
    readonly IsAlltabsVisible?: boolean;
    readonly IsAllTabVisible?: boolean;
    readonly IsLastUpdatedVisible: boolean;
    readonly IsRefereshIconVisible: boolean;
    readonly vesselSelectPosition?: "before" | "after";
    readonly filterApplyonRenderData: string;
  };
  columns: ColumnDef<Vessel>[];
  readonly isFetchingNextPage?: boolean;
  readonly isLoading?: boolean;
  readonly pagination: any; // Using `any` for simplification as the exact type is complex

  readonly componentView: {
    readonly gridComponent?: GridComponentType;
    readonly defaultComponent?: "list" | "grid";
  };
}


export const findMaxBarChartValue = (data: any[]): number => {
  if (!data || data.length === 0) {
    return 0;
  }

  const values = data.map(item => item.countforBarChart);
  return Math.max(...values);
};

export default function CardModule({
  title,
  vessels,
  staticData,
  visibleConfig,
  multiVesselSelects = [],
  componentView,
  sizeKey = "md",
  onRefresh,
  onSendEmail,
  onVesselClick,
  fetchNextPage,
  onChangeActiveTab,
  isFetchingNextPage,
  isLoading,
  pagination,
  columns: columnsProp,
}: Readonly<CardModuleProps>) {
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    componentView?.defaultComponent || "list"
  );
  const [activeTab, setActiveTab] = useState<string>(() => {
    if (visibleConfig?.IsAllTabVisible) {
      return "All"; // default to All tab when visible
    }
    return staticData.tabs[0] || ""; // default to first real tab
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [selectStates, setSelectStates] = useState<string[][]>(() =>
    multiVesselSelects.map(() => [])
  );

  const filterKeyMapping = {
    vessel_id: { dropdownKey: "vessel_id", renderKey: "vessel_id" },
    vessel_code: {
      dropdownKey: "vessel_account_code_new",
      renderKey: "vessel_code",
    },
  };

   const handleTabChange = useCallback(
    (tab: string) => {
      setActiveTab(tab); // Update the local state
      onChangeActiveTab(tab); // Call the parent's callback
    },
    [onChangeActiveTab]
  );
  
  const filteredVessels = useMemo(() => {
    if (!vessels) return [];

    let filteredData = [...vessels];

    // Filter by Active Tab
    if (activeTab !== "All") {
      filteredData = filteredData.filter((v: Vessel) => v.type === activeTab);
    }

    // Filter 1: Vessel Name
    const vesselNameSelections = selectStates[0];
    if (vesselNameSelections?.length > 0) {
      const allVesselOptions =
        multiVesselSelects[0]?.groups.flatMap((g) => g.vessels) || [];

      // Use the mapping to get the correct keys
      const keys =
        filterKeyMapping[visibleConfig.filterApplyonRenderData] ||
        filterKeyMapping.vessel_id;

      //selectedVesselIdentifiers set of  vessel_id's of all selecte active vessels names from the first select box
      const selectedVesselIdentifiers = new Set(
        allVesselOptions
          .filter((opt) => vesselNameSelections.includes(opt.name))
          .map((opt) => `${opt[keys.dropdownKey]}`)
      );

      //the filteredData array contains objects with vessel.id, so filter out them to render on table or grid view
      if (selectedVesselIdentifiers.size > 0) {
        filteredData = filteredData.filter((vessel) =>
          selectedVesselIdentifiers.has(`${vessel[keys.renderKey]}`)
        );
      }
    }

    // Filter 2: Level RA
    const levelRaSelections = selectStates[1];
    if (levelRaSelections?.length > 0) {
      filteredData = filteredData.filter((vessel: Vessel) => {
        return levelRaSelections.includes(vessel.ra_level);
      });
    }

    return filteredData;
  }, [vessels, activeTab, selectStates, visibleConfig.filterApplyonRenderData]);

  // update the maxRange for barchart 
   staticData.barChartMaxRange = findMaxBarChartValue(filteredVessels) ;

//   Use useMemo to calculate the chart data. It will only re-run when filteredVessels changes.
  const chartData = useMemo(() => {
    // Define a default empty chart data object
    const defaultChartData = {
      openDeficiencies: {
        title: "Open Deficiencies (Not accepted by office)",
        total: 0,
        data: [
          { label: "Overdue", value: 0, color: "#d80e61" },
          { label: "Due within 30 days", value: 0, color: "#fbc02d" },
          { label: "Others", value: 0, color: "#27a527" },
        ],
      },
      closedDeficiencies: {
        title: "Closed Deficiencies (Accepted by office)",
        total: 0,
        data: [
          { label: "High", value: 0, color: "#d80e61" },
          { label: "Medium", value: 0, color: "#fbc02d" },
          { label: "Low", value: 0, color: "#27a527" },
        ],
      },
    };

    // If filteredVessels is empty, return the default data immediately.
    if (!Array.isArray(filteredVessels) || filteredVessels.length === 0) {
      return defaultChartData;
    }

    // Otherwise, perform the calculations as before.
    const totalOverdue = filteredVessels.reduce(
      (sum, item: any) => sum + (item.overdue || 0),
      0
    );
    const totalDueWithin30Days = filteredVessels.reduce(
      (sum, item: any) => sum + (item.due_within_30_days || 0),
      0
    );
    const totalOthers = filteredVessels.reduce(
      (sum, item: any) => sum + (item.others || 0),
      0
    );
    const totalOpenDeficiencies =
      totalOverdue + totalDueWithin30Days + totalOthers;

    // Aggregate data for closed deficiencies.
    const closedDeficiencyTotals = filteredVessels.reduce(
      (acc, item: any) => {
        acc.high += item.severity?.high || 0;
        acc.medium += item.severity?.medium || 0;
        acc.low += item.severity?.low || 0;
        return acc;
      },
      { high: 0, medium: 0, low: 0 }
    );

    const totalClosedDeficiencies = filteredVessels.reduce(
      (sum, item: any) => sum + (item.accepted_by_office || 0),
      0
    );

    return {
      openDeficiencies: {
        title: "Open Deficiencies (Not accepted by office)",
        total: totalOpenDeficiencies,
        data: [
          { label: "Overdue", value: totalOverdue, color: "#d80e61" },
          {
            label: "Due within 30 days",
            value: totalDueWithin30Days,
            color: "#fbc02d",
          },
          { label: "Others", value: totalOthers, color: "#27a527" },
        ],
      },
      closedDeficiencies: {
        title: "Closed Deficiencies (Accepted by office)",
        total: totalClosedDeficiencies,
        data: [
          {
            label: "High",
            value: closedDeficiencyTotals.high,
            color: "#d80e61",
          },
          {
            label: "Medium",
            value: closedDeficiencyTotals.medium,
            color: "#fbc02d",
          },
          { label: "Low", value: closedDeficiencyTotals.low, color: "#27a527" },
        ],
      },
    };
  }, [filteredVessels, visibleConfig.filterApplyonRenderData]); // Added a dependency for safety

  const handleRefresh = useCallback(() => {
    onRefresh();
    setLastUpdated(new Date());
  }, [onRefresh]);

  const handleSelectChange = useCallback(
    (index: number, newSelected: string[]) => {
      setSelectStates((prevStates) => {
        const newStates = [...prevStates];
        newStates[index] = newSelected;
        return newStates;
      });
    },
    []
  );

  const hasVesselsData = filteredVessels && filteredVessels.length > 0;
  const finalIsiconRenderVisible = hasVesselsData
    ? visibleConfig.IsiconRenderVisible
    : false;
  const finalIsenLargeIconVisible = hasVesselsData
    ? visibleConfig.IsenLargeIconVisible
    : false;

  const renderViewContent = () =>
    viewMode === "list" ? (
      <CardTable
        vessels={filteredVessels}
        columns={columnsProp} // Pass the columns array here
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage ?? false}
        isLoading={isLoading ?? false}
        fetchNextPage={fetchNextPage}
      />
    ) : (
      <CardGrid
        vessels={filteredVessels}
        tableHeaders={staticData.tableHeaders}
        badgeColors={staticData.badgeColors}
        chartData={staticData.chartData}
        barChartMaxRange={staticData.barChartMaxRange}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        fetchNextPage={fetchNextPage}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        gridComponent={componentView?.gridComponent}
        
      />
    );

  const renderModuleCore = () => (
    <>
      <CardModuleHeader
        title={title}
        viewMode={viewMode}
        isModal={isModalOpen}
        IsiconRenderVisible={finalIsiconRenderVisible}
        IsenLargeIconVisible={finalIsenLargeIconVisible}
        onViewModeChange={setViewMode}
        onToggleModal={() => setIsModalOpen(!isModalOpen)}
      />

      {visibleConfig.IsLastUpdatedVisible && (
        <div className="ra-last-updated-container">
          <p className="ra-last-updated-text">
            Last Updated on:{" "}
            {`${lastUpdated.toLocaleDateString(undefined, {
              day: "2-digit",
              month: "short",
              year: "numeric",
            })} ${lastUpdated.toLocaleTimeString(undefined, {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            })}`}
            {visibleConfig.IsRefereshIconVisible && (
              <RotateCw onClick={handleRefresh} className="ra-refresh-icon" />
            )}
          </p>
        </div>
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === "before" &&
        viewMode === "list" && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
          />
        )}

      {visibleConfig.IsAlltabsVisible && (
        <CardTabs
          tabs={staticData.tabs}
          activeTab={activeTab}
          IsAlltabsVisible={visibleConfig.IsAlltabsVisible}
          IsAllTabVisible={visibleConfig.IsAllTabVisible}
          onTabChange={handleTabChange}
        />
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === "after" &&
        viewMode === "list" && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
          />
        )}

      {renderViewContent()}
    </>
  );

  return (
    <>
      <div
        className={classNames("ra-vessel-card-container", `size-${sizeKey}`)}
      >
        {renderModuleCore()}
      </div>

      {isModalOpen && (
        <ModuleModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          sizeKey={sizeKey}
        >
          {renderModuleCore()}
        </ModuleModal>
      )}
    </>
  );
}
