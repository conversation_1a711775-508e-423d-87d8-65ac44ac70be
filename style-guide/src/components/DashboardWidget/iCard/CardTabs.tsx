import React, { useMemo } from "react";
import classNames from "classnames";
import "./styles/CardContainer.scss";

interface VesselTabsProps {
  tabs: string[];
  activeTab: string;
  IsAlltabsVisible?: boolean;
  IsAllTabVisible?: boolean;
  onTabChange: (tab: string) => void;
}

export const CardTabs: React.FC<VesselTabsProps> = ({
  tabs,
  activeTab,
  IsAllTabVisible,
  onTabChange,
}) => {
  if (!tabs || tabs.length === 0) return null;

  const displayTabs = useMemo(() => {
    const cleanTabs = tabs.filter((t) => t !== "All");
    return IsAllTabVisible ? ["All", ...cleanTabs] : cleanTabs;
  }, [tabs, IsAllTabVisible]);

  return (
    <div className="ra-tabs-container">
      {displayTabs.map((tab) => (
        <button
          key={tab}
          onClick={() => onTabChange(tab)}
          className={classNames("ra-tab-button", { active: activeTab === tab })}
        >
          {tab}
          {activeTab === tab && <span className="ra-active-tab-indicator" />}
        </button>
      ))}
    </div>
  );
};
