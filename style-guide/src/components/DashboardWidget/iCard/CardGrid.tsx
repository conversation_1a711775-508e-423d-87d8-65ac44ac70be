import React from 'react';
import { useInfiniteScroll } from '../hooks/useInfiniteScroll';
import Spinner from './Spinner';
import BarChart from '../common/BarChart';
import { DonutChartAdapter } from '../common/DonutChartAdapter';
import Dashboard from '../common/DashboardGrid/Dashboard';
import { CardGridProps } from '../types/card-types';
import './styles/CardGrid.scss';

export default function CardGrid({
  vessels,
  tableHeaders,
  badgeColors,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  gridComponent = 'bar',
  ...rest
}: Readonly<CardGridProps>) {
  const hasNextPage = pagination && pagination.page < pagination.totalPages;

  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    dataLength: vessels?.length || 0,
  });

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="spinner-container">
          <Spinner />
        </div>
      );
    }
    if (vessels.length === 0) {
      return <div className="no-results-cell">No results found</div>;
    }

    // --- DYNAMIC RENDERING LOGIC ---
    if (gridComponent === 'pie') {
      // If the prop says 'pie', render the Donut Chart via the adapter
      return (
        <DonutChartAdapter
          vessels={vessels}
          tableHeaders={tableHeaders}
          badgeColors={badgeColors}
          pagination={pagination}
          isFetchingNextPage={isFetchingNextPage}
          isLoading={isLoading}
          fetchNextPage={fetchNextPage}
          {...rest}
        />
      );
    }

    if (gridComponent === 'dashboard') {
      // If the prop is 'dashboard', render your DashboardComponent
      // Pass the necessary props like vessels, colors, etc.
      return (
        <Dashboard
          vessels={vessels}
          badgeColors={badgeColors}
          //   tableHeaders={tableHeaders}
        />
      );
    }

    // Otherwise, render the Bar Chart by default
    const chartData = vessels.map((vessel) => {
      const values: Record<string, number | string> = { name: vessel.name };
      tableHeaders
        .filter((h) => h !== 'Vessel' && h !== 'Action')
        .forEach((header, idx) => {
          values[header] = vessel.vesselData[idx];
        });
      return values;
    });

    return (
      <div className="vessel-bar-chart-wrapper">
        <BarChart
          vessels={chartData}
          valueHeaders={tableHeaders.filter((h) => h !== 'Vessel' && h !== 'Action')}
          badgeColors={badgeColors}
          valueDomain={[0, 250]}
          isModal={true}
          {...rest}
        />
      </div>
    );
  };

  return (
    <div ref={containerRef} onScroll={handleScroll} className="vessel-grid-root">
      {renderContent()}
      {isFetchingNextPage && (
        <div className="loading-indicator">
          <Spinner />
        </div>
      )}
    </div>
  );
}
