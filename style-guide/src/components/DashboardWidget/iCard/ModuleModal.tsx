import React from "react";
import classNames from "classnames";
import "./styles/CardContainer.scss";

interface ModuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  sizeKey: "sm" | "md" | "lg";
  children: React.ReactNode;
}

export const ModuleModal: React.FC<ModuleModalProps> = ({
  isOpen,
  onClose,
  sizeKey,
  children,
}) => {
  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) onClose();
  };

  return (
    <div className="ra-vessel-card-modal-overlay" onClick={handleOverlayClick}>
      <div
        className={classNames("ra-vessel-card-modal", `size-${sizeKey}`)}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  );
};
