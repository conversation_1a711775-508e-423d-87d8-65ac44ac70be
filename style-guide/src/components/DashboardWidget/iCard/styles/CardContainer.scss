@use "./variables" as *;

.ra-vessel-selects-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.ra-vessel-selects-container .ra-selectWrapper {
  flex: 1 1 auto;
  min-width: 200px;
  max-width: 100%;
}

.ra-vessel-card-container {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  border: 1px solid #dee2e6 !important;
  border-radius: 5.2px !important;
  box-shadow: 0px 0px 3.47px 0px rgba(0, 0, 0, 0.149);
  background: #ffffff;

  //width will be set by the parent component
  &.size-sm {
    // width: 500px;
    height: 400px;
  }

  &.size-md {
    // width: 696px; // according to figma
    height: 490px;
  }

  &.size-lg {
    // width: 1000px;
    height: 720px;
  }

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.ra-vessel-card-modal-overlay {
  all: unset;
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  cursor: pointer;
  padding: 0;
  border: none;
}

.ra-vessel-card-modal {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  padding: 2.5rem;
  position: relative;
  cursor: auto;

  &.size-sm {
    width: 520px;
    height: 420px;
  }

  &.size-md {
    width: 800px;
    height: 600px;
  }

  &.size-lg {
    width: 1000px;
    height: 720px;
  }
}

.ra-vessel-module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;

  gap: 1rem;
}

.ra-vessel-module-title {
  font-size: 16px;
  font-weight: 600;
  color: $color-fleet-blue;
}

.ra-vessel-module-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.ra-view-toggle-container {
  background-color: color(display-p3 0.9569 0.9647 0.9725);
  padding-bottom: 0.1rem;
  padding-left: 0.3rem;
  padding-top: 0.3rem;
  border-radius: 0.25rem;
}

.ra-view-toggle-button {
  padding: 0.1rem;
  padding-bottom: 0.01rem !important;
  border-radius: 0.25rem;
  margin-right: 0.3rem;
  border: none;

  &.active {
    background-color: $color-fleet-blue;
    color: white;
  }

  &:not(.active) {
    color: $color-fleet-blue;

    &:hover {
      background-color: #bfdbfe;
    }
  }
}

.ra-view-toggle-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.ra-enlarge-icon {
  width: 1.6rem;
  height: 1.6rem;
  color: $color-fleet-blue;
  margin-left: 0.5rem;
  cursor: pointer;
}

.ra-last-updated-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.2rem;
  margin-top: 0.2rem;
}

.ra-last-updated-text {
  font-size: 12px;
  font-weight: 400;
  color: #6c757d;
  display: flex;
  align-items: center;
  margin-top: -10px;
  margin-bottom: 5px;
}

.ra-refresh-icon {
  width: 1.15rem;
  height: 1.15rem;
  margin-left: 0.3rem;
  color: $color-fleet-blue;
  cursor: pointer;
}

.ra-tabs-container {
  display: flex;
  font-weight: 200;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  border-bottom: 1px solid #e5e7eb;

  @media (max-width: 640px) {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

.ra-tab-button {
  /* Remove default button styles */
  background: none;
  color: rgba(108, 117, 125, 1);
  font-size: 14px;
  border: none;
  position: relative;
  padding-top: 0.4rem;
  padding-right: 0.4rem;
  padding-left: 0.4rem;
  padding-bottom: 0.5rem;
  cursor: pointer; /* Add a pointer cursor to indicate it's clickable */

  /* Remove the default focus outline */
  &:focus {
    outline: none;
  }

  &.active {
    color: $color-fleet-blue;
    font-weight: 500;
  }
}
.ra-active-tab-indicator {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background-color: $color-fleet-blue;
}

.ra-content-container {
  flex-grow: 1;
  overflow: hidden;
  position: relative;
}

.ra-content-container-modal {
  height: 350px;
}

.ra-content-container-non-modal {
  height: 350px;
}

.ra-close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: #4b5563;
  cursor: pointer;

  &:hover {
    color: #000;
  }
}
