import React, { useMemo, useState } from "react";
import { ColumnDef, SortingState } from "@tanstack/react-table";
import InfiniteScrollTable from "./InfiniteScrollTable";
import { Vessel } from "../types/card-types";
import "./styles/CardTable.scss";
export interface CardTableProps<T extends object> {
  vessels: T[]; // Use a generic type for flexibility
  columns: ColumnDef<T>[];
  pagination: any;
  isFetchingNextPage: boolean;
  isLoading: boolean;
  fetchNextPage: () => void;
  // You might still want to keep these for actions, or embed them in columns
  onSendEmail?: (vessel: Vessel) => void;
  onVesselClick?: (vessel: Vessel) => void;
}

// / We're no longer using getColumns, so we don't need its dependencies
// such as MailIcon, ExternalLinkIcon, Badge, etc.

export default function CardTable({
  vessels,
  columns, // The columns are now a prop
  pagination,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  onVesselClick,
  // Removed tableHeaders, badgeColors, onSendEmail as they are handled in the columns prop
}: Readonly<CardTableProps<Vessel>>): JSX.Element {
  
  // Manage sorting state internally
  const [sorting, setSorting] = useState<SortingState>([]);

  // The sorting logic now needs to find the correct data accessor based on the column ID
  const sortedVessels = useMemo(() => {
    if (!sorting.length) {
      return vessels;
    }

    const sortedData = [...vessels]; // Create a mutable copy
    const { id, desc } = sorting[0];

    // Find the column definition to get the accessor key or custom sort function
    const sortColumn = columns.find(col => col.id === id || (col as any).accessorKey === id);
    
    if (!sortColumn || sortColumn.enableSorting === false) {
      return vessels;
    }

    sortedData.sort((a, b) => {
      // Use the accessorKey directly for sorting
      const accessorKey = (sortColumn as any).accessorKey;
      let valA = a[accessorKey];
      let valB = b[accessorKey];

      // Handle null or undefined values to prevent errors
      if (valA == null) return 1;
      if (valB == null) return -1;

      // Comparison logic
      if (valA < valB) return desc ? 1 : -1;
      if (valA > valB) return desc ? -1 : 1;

      return 0;
    });

    return sortedData;
  }, [vessels, sorting]); // Remove columns from dependencies to prevent infinite re-renders

  return (
    <InfiniteScrollTable
      data={sortedVessels}
      columns={columns}
      isLoading={isLoading}
      isFetchingNextPage={isFetchingNextPage}
      fetchNextPage={fetchNextPage}
      pagination={pagination}
      onRowClick={onVesselClick}
      sorting={{
        sorting,
        onSortingChange: setSorting,
      }}
    />
  );
}