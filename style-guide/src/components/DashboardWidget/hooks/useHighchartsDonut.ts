import { useMemo } from "react";
import Highcharts from "highcharts";

// Define the shape of the data items for the chart
interface ChartDataItem {
  label: string;
  value: number;
  color: string;
  url: string;
}

interface UseHighchartsDonutProps {
  data: ChartDataItem[];
  size?: any;
}

/**
 * A helper function to generate the tooltip configuration.
 * This keeps the main options object cleaner.
 */
const getTooltipOptions = (): Highcharts.TooltipOptions => ({
  useHTML: true,
  backgroundColor: "#000000",
  borderColor: "#000000",
  borderRadius: 8,
  shadow: false,
  shape: "square" as Highcharts.TooltipShapeValue,
  className: "custom-sharp-tooltip",
  headerFormat: "",
  pointFormat:
    '<div style="font-size: 16px; color: #FFFFFF; padding: 5px;">' +
    "{point.name}<br/>" +
    '<span style="color:{point.color}; font-size: 20px;">●</span> ' +
    "{point.y}</div>",
  positioner: function(labelWidth, labelHeight, point) {
    const chart = this.chart;
    const tooltip = (this as any).label;
    const { plotX, plotY } = point;
    const arrowWidth = 15;

    tooltip.removeClass("arrow-up arrow-down arrow-left arrow-right");

    let x, y;

    if (plotX + arrowWidth + labelWidth > chart.plotWidth) {
      x = plotX - labelWidth - arrowWidth;
    } else {
      x = plotX + arrowWidth;
      tooltip.addClass("arrow-left");
    }

    y = plotY - labelHeight / 2;

    if (y < 0) y = 5;
    if (y + labelHeight > chart.plotHeight)
      y = chart.plotHeight - labelHeight - 5;

    return { x, y };
  },
});

/**
 * A helper function to generate the plotOptions configuration,
 * including the click event handler for navigation.
 */
const getPlotOptions = (size): Highcharts.PlotOptions => ({
  pie: {
    innerSize: "65%",
    depth: 22,
    allowPointSelect: true,
    cursor: "pointer",
    size: size,
    point: {
      events: {
        click: function() {
          const point = this as Highcharts.Point & {
            options: { url?: string };
          };
          if (point.options.url) {
            // Use a direct window.location assignment for navigation
            window.location.href = point.options.url;
          }
        },
      },
    },
    dataLabels: {
      enabled: true,
      format: "{point.name}: <b>{point.y}</b>",
      distance: 10,
      style: {
        fontWeight: "normal",
        fontSize: "11px",
        color: "#333",
        textOutline: "none",
      },
    },
    showInLegend: true,
  },
});

export const useHighchartsDonut = ({ data, size }: UseHighchartsDonutProps) => {
  const tooltipStyle = `
    .custom-sharp-tooltip { position: relative; padding-top: 60px; }
    .custom-sharp-tooltip::before { content: ""; position: absolute; border: 7px solid transparent; z-index: 1; }
    .arrow-up::before { top: -14px; left: 50%; transform: translateX(-50%); border-bottom-color: #000000; }
    .arrow-down::before { bottom: -14px; left: 50%; transform: translateX(-50%); border-top-color: #000000; }
    .arrow-left::before { left: -14px; top: 50%; transform: translateY(-50%); border-right-color: #000000; }
    .arrow-right::before { right: -14px; top: 50%; transform: translateY(-50%); border-left-color: #000000; }
  `;

  // useMemo ensures the complex options object is only recalculated when data changes.
  const options = useMemo((): Highcharts.Options => {
    const chartData = data.map((item) => ({
      name: item.label,
      y: item.value,
      color: item.color,
      url: item.url,
    }));

    return {
      chart: {
        type: "pie",
        backgroundColor: "transparent",
        options3d: { enabled: true, alpha: 45, beta: 0 },
        // Reduce margins to give the chart more space
        margin: [0, 0, 0, 0],
        spacingTop: 0,
        spacingBottom: 0,
        spacingLeft: 0,
        spacingRight: 0,
      },
      title: { text: undefined },
      credits: { enabled: false },
      tooltip: getTooltipOptions(),
      plotOptions: getPlotOptions(size),
      legend: {
        layout: "horizontal",
        align: "center",
        verticalAlign: "bottom",
        itemStyle: {
          fontSize: "12px",
          color: "black",
        },
        // Reduce the horizontal padding between items
        itemDistance: 12,
        // Reduce the top and bottom margins for the legend box
        itemMarginTop: 0,
        itemMarginBottom: 0,
      },
      series: [{ type: "pie", name: "Count", data: chartData }],
    };
  }, [data, size]);

  return { options, tooltipStyle };
};
