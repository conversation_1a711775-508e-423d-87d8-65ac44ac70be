import { useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import Highcharts from 'highcharts';

// Define the shape of the data items for the chart
interface ChartDataItem {
  label: string;
  value: number;
  color: string;
  url: string;
}

interface UseHighchartsDonutProps {
  data: ChartDataItem[];
}

/**
 * A helper function to generate the tooltip configuration.
 * This keeps the main options object cleaner.
 */
const getTooltipOptions = (): Highcharts.TooltipOptions => ({
  useHTML: true,
  backgroundColor: '#000000',
  borderColor: '#000000',
  borderRadius: 8,
  shadow: false,
  shape: 'square' as Highcharts.TooltipShapeValue,
  className: 'custom-sharp-tooltip',
  headerFormat: '',
  pointFormat:
    '<div style="font-size: 16px; color: #FFFFFF; padding: 5px;">' +
    '{point.name}<br/>' +
    '<span style="color:{point.color}; font-size: 20px;">●</span> ' +
    '{point.y}</div>',
  positioner: function (labelWidth, labelHeight, point) {
    const chart = this.chart;
    const tooltip = (this as any).label;
    const { plotX, plotY } = point;
    const arrowWidth = 15;

    tooltip.removeClass('arrow-up arrow-down arrow-left arrow-right');

    let x, y;

    if (plotX + arrowWidth + labelWidth > chart.plotWidth) {
      x = plotX - labelWidth - arrowWidth;
    } else {
      x = plotX + arrowWidth;
      tooltip.addClass('arrow-left');
    }

    y = plotY - labelHeight / 2;

    if (y < 0) y = 5;
    if (y + labelHeight > chart.plotHeight) y = chart.plotHeight - labelHeight - 5;

    return { x, y };
  },
});

/**
 * A helper function to generate the plotOptions configuration,
 * including the click event handler for navigation.
 */
const getPlotOptions = (history: ReturnType<typeof useHistory>): Highcharts.PlotOptions => ({
  pie: {
    innerSize: '60%',
    depth: 35,
    allowPointSelect: true,
    cursor: 'pointer',
    point: {
      events: {
        click: function () {
          const point = this as Highcharts.Point & {
            options: { url?: string };
          };
          if (point.options.url) {
            history.push(point.options.url);
          }
        },
      },
    },
    dataLabels: {
      enabled: true,
      format: '{point.name}: <b>{point.y}</b>',
      distance: 20,
      style: {
        fontWeight: 'normal',
        fontSize: '14px',
        color: '#333',
        textOutline: 'none',
      },
    },
    showInLegend: true,
  },
});

/**
 * DRY PRINCIPLE / BEST PRACTICE: This hook encapsulates all the logic for building
 * the Highcharts options object, separating configuration from rendering.
 */
export const useHighchartsDonut = ({ data }: UseHighchartsDonutProps) => {
  const history = useHistory();

  // CSS styles for the tooltip are co-located with the hook that uses them.
  const tooltipStyle = `
    .custom-sharp-tooltip { position: relative; padding-top: 60px; }
    .custom-sharp-tooltip::before { content: ""; position: absolute; border: 7px solid transparent; z-index: 1; }
    .arrow-up::before { top: -14px; left: 50%; transform: translateX(-50%); border-bottom-color: #000000; }
    .arrow-down::before { bottom: -14px; left: 50%; transform: translateX(-50%); border-top-color: #000000; }
    .arrow-left::before { left: -14px; top: 50%; transform: translateY(-50%); border-right-color: #000000; }
    .arrow-right::before { right: -14px; top: 50%; transform: translateY(-50%); border-left-color: #000000; }
  `;

  // useMemo ensures the complex options object is only recalculated when data changes.
  const options = useMemo((): Highcharts.Options => {
    const chartData = data.map((item) => ({
      name: item.label,
      y: item.value,
      color: item.color,
      url: item.url,
    }));

    return {
      chart: {
        type: 'pie',
        backgroundColor: 'transparent',
        options3d: { enabled: true, alpha: 45, beta: 0 },
      },
      title: { text: undefined },
      credits: { enabled: false },
      tooltip: getTooltipOptions(),
      plotOptions: getPlotOptions(history),
      legend: {
        layout: 'horizontal',
        align: 'center',
        verticalAlign: 'bottom',
        itemStyle: { fontSize: '14px', color: '#555' },
      },
      series: [{ type: 'pie', name: 'Count', data: chartData }],
    };
  }, [data, history]);

  return { options, tooltipStyle };
};
