import { useMemo } from "react";
import * as d3 from "d3";

interface D3ChartConfig {
  vessels: any[];
  valueHeaders: string[];
  badgeColors: string[];
  valueDomain: [number, number];
  chartWidth: number;
  heightPerBar: number;
  margin: { top: number; right: number; bottom: number; left: number };
}

export const useD3Chart = ({
  vessels,
  valueHeaders,
  badgeColors,
  valueDomain,
  chartWidth,
  heightPerBar,
  margin,
}: D3ChartConfig) => {
  const chartHeight = useMemo(() => vessels.length * heightPerBar, [vessels.length, heightPerBar]);
  const totalHeight = chartHeight + margin.top + margin.bottom;

const xScale = useMemo(
  () =>
    d3
      .scaleLinear()
      .domain(valueDomain)
      .range([0, chartWidth - margin.left - margin.right])
      .nice()
      .clamp(true),
  [valueDomain, chartWidth, margin],
);

  const yScale = useMemo(
    () =>
      d3
        .scaleBand()
        .domain(vessels.map((_, i) => i.toString()))
        .range([margin.top, chartHeight + margin.top])
        .padding(0.4),
    [vessels, chartHeight, margin.top],
  );

  const barColorScale = useMemo(
    () => d3.scaleOrdinal<string, string>().domain(valueHeaders).range(badgeColors),
    [valueHeaders, badgeColors],
  );

  const textColorScale = useMemo(() => {
    const getTextColor = (bg: string) => (bg === '#fbc02d' ? 'black' : 'white');
    return d3
      .scaleOrdinal<string, string>()
      .domain(valueHeaders)
      .range(badgeColors.map(getTextColor));
  }, [valueHeaders, badgeColors]);

  const stackedBarData = useMemo(
    () =>
      vessels.map((vessel, i) => {
        let xOffset = 0;
        const segments = valueHeaders
          .map((key) => {
            const value = (vessel[key] as number) || 0;

            // Skip segments with 0 or negative values
            if (value <= 0) {
              return null;
            }

            const segmentWidth = xScale(value);

            // Add gap between segments (1px gap)
            const gap = 1; // Only add gap if value is positive

            const segment = {
              key,
              value,
              x: xOffset,
              width: segmentWidth,
              y: yScale(i.toString())!,
              height: yScale.bandwidth(),
              vesselName: vessel.name,
              vesselIndex: i,
            };

            xOffset += segmentWidth + gap;
            return segment;
          })
          .filter(
            (segment): segment is NonNullable<typeof segment> =>
              segment !== null
          );

        return { vesselName: vessel.name, segments, vesselIndex: i };
      }),
    [vessels, valueHeaders, xScale, yScale, valueDomain]
  );

  return {
    xScale,
    yScale,
    barColorScale,
    textColorScale,
    stackedBarData,
    chartHeight,
    totalHeight,
  };
};