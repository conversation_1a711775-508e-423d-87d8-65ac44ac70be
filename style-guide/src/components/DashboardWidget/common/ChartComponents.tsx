import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';

interface ScrollableChartElementsProps {
  yScale: d3.ScaleBand<string>;
  xScale: d3.ScaleLinear<number, number>;
  height: number;
  margin: { top: number; right: number; bottom: number; left: number };
  vessels: any[]; // Now receiving the full vessels data
  ticks: number[];
}
interface ScrollableChartElementsProps {
  yScale: d3.ScaleBand<string>;
  xScale: d3.ScaleLinear<number, number>;
  height: number;
  margin: { top: number; right: number; bottom: number; left: number };
  vessels: any[];
  ticks: number[];
}

export const ScrollableChartElements: React.FC<ScrollableChartElementsProps> = ({
  yScale,
  xScale,
  height,
  margin,
  vessels,
  ticks,
}) => {
  const yAxisRef = useRef<SVGGElement>(null);
  const xGridRef = useRef<SVGGElement>(null);

  useEffect(() => {
    if (yAxisRef.current) {
      const yAxis = d3.axisLeft(yScale)
        .tickFormat((d) => {
          const index = parseInt(d, 10);
          return vessels[index].name;
        })
        .tickSize(0);

      d3.select(yAxisRef.current)
        .call(yAxis)
        .selectAll('.tick text')
        .call(wrap, margin.left - 10);
    }
    
    if (xGridRef.current) {
      const xAxisGrid = d3.axisBottom(xScale)
        .tickSize(-height)
        .tickFormat(() => '')
        .tickValues(ticks);
      
      d3.select(xGridRef.current)
        .call(xAxisGrid)
        .selectAll('.tick line')
        .attr('stroke', '#ddd');
    }
  }, [yScale, xScale, height, margin.left, vessels, ticks]);

  function wrap(text: d3.Selection<any, any, any, any>, width: number) {
    text.each(function () {
      const text = d3.select(this);
      const words = text.text().split(/\s+/).reverse();
      let word;
      let line: string[] = [];
      let lineNumber = 0;
      const lineHeight = 1.1;
      const y = text.attr('y');
      const dy = parseFloat(text.attr('dy')) || 0;
      let tspan = text
        .text(null)
        .append('tspan')
        .attr('x', -10)
        .attr('y', y)
        .attr('dy', dy + 'em');
      while ((word = words.pop())) {
        line.push(word);
        tspan.text(line.join(' '));
        if ((tspan.node() as SVGTextContentElement).getComputedTextLength() > width) {
          line.pop();
          tspan.text(line.join(' '));
          line = [word];
          tspan = text
            .append('tspan')
            .attr('x', -10)
            .attr('y', y)
            .attr('dy', ++lineNumber * lineHeight + dy + 'em')
            .text(word);
        }
      }
    });
  }

  return (
    <>
      <g
        ref={xGridRef}
        transform={`translate(${margin.left}, ${height + margin.top})`}
        className="x-grid"
      />
      <g ref={yAxisRef} transform={`translate(${margin.left}, 0)`} className="y-axis" />
    </>
  );
};

// ... (Rest of the file remains the same)

// --- NEW: Component for the sticky X-Axis ---
interface StickyXAxisProps {
  xScale: d3.ScaleLinear<number, number>;
  width: number;
  height: number;
  margin: { left: number };
  ticks: number[];
}
export const StickyXAxis: React.FC<StickyXAxisProps> = ({ xScale, width, height, margin, ticks }) => {
  const xAxisRef = useRef<SVGGElement>(null);

useEffect(() => {
  if (xAxisRef.current) {
    const xAxis = d3.axisBottom(xScale)
      .tickValues(ticks);
    d3.select(xAxisRef.current)
      .call(xAxis)
      .selectAll('.tick line')
      .remove();
  }
}, [xScale, ticks]);

  return (
    <div className="x-axis-container">
      <svg width={width} height={height}>
        <g ref={xAxisRef} transform={`translate(${margin.left}, 0)`} className="x-axis" />
      </svg>
    </div>
  );
};

// --- Tooltip Component (Unchanged) ---
interface ChartTooltipProps {
  content: string;
  position: { x: number; y: number };
  isVisible: boolean;
}
export const ChartTooltip: React.FC<ChartTooltipProps> = ({ content, position, isVisible }) => {
  return (
    <div
      className="custom-tooltip"
      style={{
        opacity: isVisible ? 1 : 0,
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

// --- Legend Component (Unchanged) ---
interface ChartLegendProps {
  valueHeaders: string[];
  badgeColors: string[];
}
export const ChartLegend: React.FC<ChartLegendProps> = ({ valueHeaders, badgeColors }) => (
  <div className="vessel-bar-chart-legend">
    {valueHeaders.map((label, i) => (
      <div className="legend-item" key={label}>
        <span className="legend-color" style={{ backgroundColor: badgeColors[i] }} />
        <span className="legend-label">{label}</span>
      </div>
    ))}
  </div>
);
