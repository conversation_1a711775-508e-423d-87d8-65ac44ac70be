import React, { useMemo } from "react";
import StatCard from "./StatCard/StatCard";
import StatusIndicatorCard from "./StatusIndicatorCard/StatusIndicatorCard";
import "./Dashboard.scss";
import { Vessel } from "../../types/card-types";

// --- Define the component's props ---
interface DashboardProps {
  vessels?: Vessel[];
  badgeColors: string[];
}

// --- Define the structure for a single status card ---
interface StatusInfo {
  label: string;
  value: number;
  color: string;
}

// --- NEW REUSABLE SUB-COMPONENT ---
// This component renders a single section (e.g., "CRITICAL")
const DashboardSection: React.FC<{ title: string; statuses: StatusInfo[] }> = ({
  title,
  statuses,
}) => (
  <div className="dashboard-section">
    <h3 className="section-title">{title.toUpperCase()}</h3>
    <div className="status-grid">
      {statuses.map((status, index) => (
        <StatusIndicatorCard
          key={index}
          value={status.value}
          label={status.label}
          color={status.color}
        />
      ))}
    </div>
  </div>
);

// --- Default Props ---
const defaultProps: Pick<DashboardProps, "vessels" | "badgeColors"> = {
  vessels: [],
  badgeColors: ["#4caf50", "#8bc34a", "#fbc02d", "#f44336"],
};

const Dashboard: React.FC<DashboardProps> = (props) => {
  const { vessels, badgeColors } = { ...defaultProps, ...props };

  // useMemo will re-calculate the dashboard stats only when the vessels prop changes.
  const { topStats, groupedStatusData } = useMemo(() => {
    if (!vessels || vessels.length === 0) {
      return { topStats: [], groupedStatusData: [] };
    }

    // --- Initialize counters ---
    const totalSubmitted = vessels.length;
    let unassignedCount = 0;

    // This will hold the data grouped by Level of R.A.
    // e.g., { Critical: { Approved: 2, Pending: 1, ... }, Special: { ... } }
    const statusGroups: { [level: string]: { [status: string]: number } } = {};

    // --- Process the vessels data ---
    vessels.forEach((vessel) => {
      const levelOfRa = (vessel.vesselData[1] as string) || "Unknown";
      const status = (vessel.vesselData[2] as string) || "Unknown";

      if (levelOfRa === "Unassigned") {
        unassignedCount++;
      }

      // Initialize the group if it doesn't exist
      if (!statusGroups[levelOfRa]) {
        statusGroups[levelOfRa] = {};
      }
      // Initialize the status count within the group if it doesn't exist
      if (!statusGroups[levelOfRa][status]) {
        statusGroups[levelOfRa][status] = 0;
      }
      // Increment the count
      statusGroups[levelOfRa][status]++;
    });

    // --- Format the data for rendering ---
    const formattedTopStats = [
      { title: "Total Risk Assessment Submitted", value: totalSubmitted },
      { title: "Unassigned Risk Assessments", value: unassignedCount },
    ];

    const statusToColorMap: { [key: string]: string } = {
      Approved: badgeColors[0],
      "Approved with Condition": badgeColors[1],
      Pending: badgeColors[2],
      Rejected: badgeColors[3],
    };

    // Convert the grouped object into an array for rendering
    const formattedGroupedData = Object.entries(statusGroups)
      .filter(([level]) => level !== "Unassigned") // Don't create a section for "Unassigned"
      .map(([level, statuses]) => ({
        title: level,
        statuses: Object.entries(statuses).map(([status, count]) => ({
          label: status,
          value: count,
          color: statusToColorMap[status] || "#808080", // Fallback color
        })),
      }));

    return {
      topStats: formattedTopStats,
      groupedStatusData: formattedGroupedData,
    };
  }, [vessels, badgeColors]);

  return (
    <div className="dashboard-container">
      <div className="dashboard-grid">
        {topStats.map((stat, index) => (
          <StatCard key={index} title={stat.title} value={stat.value} />
        ))}
      </div>

      {/* Dynamically render a section for each Level of R.A. */}
      {groupedStatusData.map((group) => (
        <DashboardSection
          key={group.title}
          title={group.title}
          statuses={group.statuses}
        />
      ))}
    </div>
  );
};

export default Dashboard;
