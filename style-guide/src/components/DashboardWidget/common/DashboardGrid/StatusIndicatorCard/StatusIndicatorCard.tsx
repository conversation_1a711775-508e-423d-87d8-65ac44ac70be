import React from 'react';
import './StatusIndicatorCard.scss';

interface StatusIndicatorCardProps {
  value: number;
  label: string;
  color: string; // e.g., '#e53935', 'gold', 'green'
}

const StatusIndicatorCard: React.FC<StatusIndicatorCardProps> = ({ value, label, color }) => {
  // Style object for the colored border
  const borderStyle = {
    borderLeft: `5px solid ${color}`,
  };

  return (
    <div className="status-indicator-card" style={borderStyle}>
      <span className="status-card-value">{value}</span>
      <span className="status-card-label">{label}</span>
    </div>
  );
};

export default StatusIndicatorCard;
