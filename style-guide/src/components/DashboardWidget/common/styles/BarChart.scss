svg {
  font-family: sans-serif;
  .domain {
    display: none;
  }
  .tick line {
    stroke: #ccc;
  }
  .tick text {
    fill: #000000;
    font-size: 12px !important;
  }
}

.xAxix-legend-parent {
  position: sticky;
  bottom: 0;
  z-index: 10;
  margin-top: 15px;
}

.x-axis-scroll-container {
  width: 100%;
  overflow-x: auto;
  height: 40px;

  .x-axis-container {
    background: white;
    //   border-top: 1px solid #ddd;
    margin-top: 20px;
    width: 100%;

    svg {
      display: block;
      min-width: 100%;
    }
  }
}

.ra-vessel-bar-chart-container {
  position: relative;
  width: 100%;
  height: 340px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.custom-tooltip {
  position: fixed;
  background: rgb(0, 0, 0);
  color: white;
  border-radius: 4px;
  padding: 10px;
  font-weight: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.2s;
  margin-top: -50px;

  &::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-right-color: black;
    filter: drop-shadow(-2px 0 1px rgba(0, 0, 0, 0.1));
  }

  .tooltip-title {
    font-weight: 300;
  }

  .tooltip-row {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .tooltip-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 12px;
  }
}

.bar-group {
  cursor: pointer;
}

.chart-scroll-container {
  flex: 1;
  overflow: auto;
  position: relative;
}

.main-chart {
  display: block;
}

.x-axis-container {
  background: white;
}

.tooltip {
  position: absolute;
  padding: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  pointer-events: none;
  // font-size: 12px;
}
.bar-subgroup {
  text {
    font-size: 10px;
    font-weight: 500;
  }
}

.y-axis text {
  font-size: 14px;
  font-weight: 550;
  fill: #1c1919;
}

/* Legend styles */
.vessel-bar-chart-legend {
  display: flex;
  gap: 20px;
  padding: 10px 0;
  padding-left: 220px;
  font-size: 12px;
  background-color: white;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .legend-color {
    width: 14px;
    height: 14px;
    border-radius: 15px;
    display: inline-block;
  }

  .legend-label {
    font-weight: 500;
    color: #333;
  }
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sticky-bottom-container {
  position: sticky;
  bottom: 0;
  z-index: 10;
  background: white;
  border-top: 1px solid #ddd;
  margin-top: auto;
}

.sticky-x-axis {
  width: 100%;
  overflow: hidden;
  position: relative;

  svg {
    display: block;
  }

  .x-axis {
    transition: transform 0.1s ease;
  }
}

.x-axis {
  .tick line {
    stroke: #ccc;
  }

  .tick text {
    fill: #858383;
    font-size: 12px;
  }
}

.x-axis .domain {
  display: none;
  stroke: none;
}
