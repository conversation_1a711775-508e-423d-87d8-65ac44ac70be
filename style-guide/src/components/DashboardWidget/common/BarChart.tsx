import React, { useState, useRef, useEffect } from "react";
import { useHistory } from "react-router-dom";
import {
  ScrollableChartElements,
  StickyXAxis,
  ChartTooltip,
  ChartLegend,
} from "./ChartComponents";
import { useD3Chart } from "../hooks/useD3Chart";
import "./styles/BarChart.scss";
import { Vessel } from "../types/card-types";
import * as d3 from "d3";

interface BarChartProps {
  vessels: any[];
  width?: number;
  heightPerBar?: number;
  valueHeaders: string[];
  badgeColors: string[];
  valueDomain: [number, number];
  isModal?: boolean;
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
}

const MARGIN = { top: 15, right: 15, bottom: 40, left: 90 };

export default function BarChart({
  vessels,
  width = 700,
  heightPerBar = 45,
  valueHeaders,
  badgeColors,
  valueDomain = [0, 250],
  isModal,
  ...rest
}: BarChartProps) {
  const history = useHistory();
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const chartWidth = isModal ? 700 : Math.max(containerWidth, 700);

  const [tooltip, setTooltip] = useState({
    visible: false,
    content: "",
    x: 0,
    y: 0,
  });

const generateTicks = () => {
    const [min, max] = valueDomain;

    // Use a custom function to round up the maximum value to a "nice" number
    const niceMax = d3.tickStep(0, max, 10) * Math.ceil(max / d3.tickStep(0, max, 10));

    // Generate ticks based on the "nice" max value and a reasonable number of steps (e.g., 10)
    const ticks = d3.range(min, niceMax + 1, d3.tickStep(0, niceMax, 10));

    // Ensure the last tick is always the final niceMax value
    if (ticks[ticks.length - 1] !== niceMax) {
        ticks.push(niceMax);
    }
    
    return ticks;
};

  const ticks = generateTicks();

  const {
    xScale,
    yScale,
    barColorScale,
    textColorScale,
    stackedBarData,
    chartHeight,
    totalHeight,
  } = useD3Chart({
    vessels,
    valueHeaders,
    badgeColors,
    valueDomain,
    chartWidth,
    heightPerBar,
    margin: MARGIN,
  });

  const handleMouseOver = (event: React.MouseEvent, segment: any) => {
    const content = `
      <div class="tooltip-content">
        <div class="tooltip-title"><b>${segment.key}</b></div>
        <div class="tooltip-row">
          <span class="tooltip-color" style="background:${barColorScale(
            segment.key
          )}"></span>
          ${segment.value}
        </div>
      </div>`;
    setTooltip({
      visible: true,
      content,
      x: event.clientX + 20,
      y: event.clientY,
    });
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (tooltip.visible) {
      setTooltip((prev) => ({
        ...prev,
        x: event.clientX + 20,
        y: event.clientY,
      }));
    }
  };

  const handleMouseOut = () => {
    setTooltip((prev) => ({ ...prev, visible: false }));
  };

  // update for bar click
  const handleClick = (vesselName: string, statusKey: string) => {
    const vesselSlug = vesselName.toLowerCase().replace(/\s+/g, "-");
    const statusSlug = statusKey.toLowerCase().replace(/\s+/g, "-");
    history.push(`/vessel/${vesselSlug}/${statusSlug}`);
  };

  return (
    <div className="ra-vessel-bar-chart-container">
      {/* Main scroll container */}
      <div ref={chartContainerRef} className="chart-scroll-container">
        {/* Chart content */}
        <div className="chart-content" style={{ width: chartWidth }}>
          <svg width={chartWidth} height={totalHeight} className="main-chart">
            <g>
              <ScrollableChartElements
                yScale={yScale}
                xScale={xScale}
                height={chartHeight}
                margin={MARGIN}
                vessels={vessels}
                ticks={ticks}
              />
              {stackedBarData.map((bar) => (
                <g
                  key={bar.vesselIndex}
                  transform={`translate(${MARGIN.left}, 0)`}
                >
                  {bar.segments.map((segment) => (
                    <g
                      key={segment.key}
                      className="bar-subgroup"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        handleClick(segment.vesselName, segment.key)
                      }
                      onMouseOver={(e) => handleMouseOver(e, segment)}
                      onMouseMove={handleMouseMove}
                      onMouseOut={handleMouseOut}
                    >
                      <rect
                        x={segment.x}
                        y={segment.y}
                        width={segment.width}
                        height={segment.height}
                        fill={barColorScale(segment.key)}
                      />
                      <text
                        x={segment.x + segment.width / 2}
                        y={segment.y + segment.height / 2}
                        dy=".35em"
                        textAnchor="middle"
                        fill={textColorScale(segment.key)}
                      >
                        {segment.value}
                      </text>
                    </g>
                  ))}
                </g>
              ))}
            </g>
          </svg>
        </div>
      </div>

      <div className="xAxix-legend-parent ">
        <StickyXAxis
          xScale={xScale}
          width={chartWidth}
          height={MARGIN.bottom}
          margin={{ left: MARGIN.left }}
          ticks={ticks} 
        />
        <ChartLegend
          valueHeaders={valueHeaders}
          badgeColors={badgeColors}
        />
      </div>

      <ChartTooltip
        content={tooltip.content}
        position={{ x: tooltip.x, y: tooltip.y }}
        isVisible={tooltip.visible}
      />
    </div>
  );
}