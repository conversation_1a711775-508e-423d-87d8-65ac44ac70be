import React from 'react';
import classNames from 'classnames';
import Dynamic3DDonutChart from './Dynamic3DDonutChart';
import './styles/DonutChartCard.scss';

interface ChartItem {
  label: string;
  value: number;
  color: string;
}

interface DonutChartCardProps {
  title: string;
  data: ChartItem[];
  size?: number | string;
}

export const DonutChartCard: React.FC<DonutChartCardProps> = ({
  title,
  data,
  size
}) => {
  const formattedData = data.map((item) => ({
    label: item.label,
    value: item.value,
    color: item.color,
    url: `/deficiency-details/${item.label.toLowerCase().replace(/\s+/g, '-')}`,
  }));

  return (
    <div className="donut-chart-card">
      <div className="donut-chart-header">
        <span className="donut-chart-title">{title}</span>
      </div>
      <div className="donut-chart-content-wrapper">
        <div className="donut-chart-visual">
          <Dynamic3DDonutChart data={formattedData} size={size} />
        </div>
      </div>
    </div>
  );
};