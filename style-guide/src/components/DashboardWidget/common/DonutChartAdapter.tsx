// --- ADAPTER COMPONENT for the Donut Chart ---
// This component's job is to transform the data from the format VesselGrid
// receives into the format that Dynamic3DDonutChart requires.

import React, { useMemo } from "react";
import { VesselDisplayProps } from "../types/card-types";
import Dynamic3DDonutChart from "./Dynamic3DDonutChart";

export const DonutChartAdapter = (props: VesselDisplayProps) => {
  // This transformation logic creates the `chartData` array in the exact
  // format required by `Dynamic3DDonutChart`, as per your example.
  const chartData = useMemo(() => {
    if (!props.vessels) return [];
    return props.vessels.map((vessel, index) => ({
      label: vessel.name,
      // Example data transformation: Summing up the vessel's data points.
      // You can adjust this logic based on what you want the chart to represent.
      value: vessel.vesselData.reduce((sum, current) => sum + current, 0),
      color: props.badgeColors[index % props.badgeColors.length],
      url: `/vessel/${vessel.name.toLowerCase().replace(/\s+/g, "-")}`,
    }));
  }, [props.vessels, props.badgeColors]);

  // Render the donut chart, wrapped in the padded div from your example.
  return (
    <div
      style={{
        padding: "2rem",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
      }}
    >
      <Dynamic3DDonutChart data={chartData} />
    </div>
  );
};
