import React from 'react';
import { DeficiencyCountCard } from './DeficiencyCountCard';
import { DonutChartCard } from './DonutChartCard';
import { ChartData } from '../types/card-types';
import './styles/DonutChartCard.scss';

interface DashboardGridProps {
  chartData: ChartData;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({ chartData }) => {
  const { openDeficiencies, closedDeficiencies } = chartData;

  const dueDateData = {
    title: 'Due Date',
    data: openDeficiencies.data, // Use data from openDeficiencies
  };

  const severityData = {
    title: 'Severity',
    data: closedDeficiencies.data, // Use data from closedDeficiencies
  };

  return (
    <div className="dashboard-grid-container">
      <DeficiencyCountCard
        title={openDeficiencies.title}
        total={openDeficiencies.total}
      />
      <DeficiencyCountCard
        title={closedDeficiencies.title}
        total={closedDeficiencies.total}
      />
      <DonutChartCard
        title={dueDateData.title}
        data={dueDateData.data}
        size="80%"
      />
      <DonutChartCard
        title={severityData.title}
        data={severityData.data}
        size="80%" 
      />
    </div>
  );
};