import React from 'react';
import './styles/DonutChartCard.scss';

interface DeficiencyCountCardProps {
  title: string;
  total: number;
}

export const DeficiencyCountCard: React.FC<DeficiencyCountCardProps> = ({
  title,
  total,
}) => {
  return (
    <div>
      <div className="deficiency-count-card">
        <div className="deficiency-count-header">
          <span className="deficiency-count-title">{title}</span>
        </div>
        <div className="deficiency-total-count-wrapper">
          <span className="deficiency-total-count">{total}</span>
        </div>
      </div>
    </div>
  );
};
