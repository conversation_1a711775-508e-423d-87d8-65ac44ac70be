import React from "react";
import "./styles/youtube-embed.scss";

interface Props {
  embedId: number | string;
}

const YoutubeEmbed = ({ embedId }: Props) => (
  <div className="video-responsive">
    <iframe
      width="531"
      height="248"
      src={`https://www.youtube.com/embed/${embedId}`}
      frameBorder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowFullScreen
      title="Embedded youtube"
    />
  </div>
);

export default YoutubeEmbed;
