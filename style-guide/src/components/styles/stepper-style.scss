.stepper-lbl {
  color: #1f4a70;
}
.stepper-overlay-loader {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  margin-top: 0.5rem;
}

.fs-16 {
  font-size: 16px;
}
.mt-3 {
  margin-top: 16px;
}

.fw-600 {
  font-weight: 600;
}

.fs-14 {
  font-size: 14px;
}

.fw-500 {
  font-weight: 500;
}

.secondary-color {
  color: #6c757d;
}

.primary-txt-color {
  color: #333333 !important;
}

.stepper-card {
  border: 1px solid #CCCCCC !important;
  box-shadow: none !important;
  border-radius: 6px;
  padding: 16px 12px;
  gap: 8px;
  cursor: default;
  opacity: 0.6 !important;
}

.stepper-card-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stepper-card-active {
  border: 2px solid #A6CBF3 !important;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2) !important;
  opacity: 1 !important;

}

.stepper-card-completed {
  opacity: 1 !important;
  cursor: pointer !important;
}

.stepper-card-lbl {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333333 !important;
}

.stepper-card-step {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #1f4a70 !important;
}

.stepper-card-unchecked-crl {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #CCCCCC;
}

.stepper-card-row {
  align-items: center;
  justify-content: space-between;
}

.stepper-bottom-container {
  height: 58px;
  background-color: #f8f9fa;
  border-top: 1px solid #cccccc;
  text-align: center;
  padding-top: 10px;
}

.stepper-bottom-button {
  padding-left: 100px;
  padding-right: 100px;
  margin-right: 8px;
  &:last-child {
    margin-right: 0;
  }
}

.stepper-breadcrumb-container {
  margin-left: 15px;
  margin-right: 15px;
  color: #1F4A70;
  justify-content: space-between;
  display: flex;
  align-items: center;
}

.stepper-breadcrumb-title {
  font-size: 24px;
  font-weight: 600;
  color: #1F4A70;
}

.stepper-close-btn {
  background: none;
  border: none;
  padding: 0;
  margin-left: 8px;
  cursor: pointer;
  vertical-align: middle;
}

.stepper-card-main {
  padding: 1rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  height: calc(100vh - 210px);
  overflow-y: auto;
  overflow-x: hidden;
}
.stepper-container-height {
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 210px);
}
.stepper-btm-sec-btn {
  background-color: #1f4a70 !important;

  &:hover,
  &:focus,
  &:active {
    background-color: darken(#1f4a70, 10%) !important;
  }
}