import React, { CSSProperties } from 'react';
import IcoMoon from 'react-icomoon';
const iconSet = require('../selection.json');

interface Props {
    color?: string;
    icon: string;
    className?: string;
    size?: number | string;
    style?: CSSProperties | any;
}

const Icon = ({ className, size, style = {}, icon, color, ...props }: Props) => {
  const spanStyle = style || {};
  if (size) {
    spanStyle.height = size;
    spanStyle.width = size;
  }

  const spanClassName = ['paris2-icon', className].filter(Boolean).join(' ');

  return (
    <span className={spanClassName} style={style} {...props}>
      <IcoMoon iconSet={iconSet} color={color} icon={icon} removeInlineStyle={true}/>
    </span>
  );
};

export default Icon;
