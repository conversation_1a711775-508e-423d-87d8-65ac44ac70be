import React from 'react';
import { Breadcrumb } from 'react-bootstrap';
import './styles/breadcrumb-header.scss';

export interface BreadcrumbItem {
  title: string | null;
  link?: string;
  linkProps?: Record<string, any>;
}

interface BreadcrumbHeaderProps {
  items: BreadcrumbItem[];
  activeItem: string | null;
  linkAs?: React.ElementType;
  
}

const BreadcrumbHeader = ({ items = [], activeItem, linkAs }: BreadcrumbHeaderProps) => (
  <Breadcrumb className="bread-crump-wrapper">
    {items.map((item) => {
      const isItemActive = item.title === activeItem;
      const className = isItemActive ? 'breadcrumb-text' : 'inactive-breadcrumb-text';
      return (
        <Breadcrumb.Item
          linkAs={linkAs}
          key={item.title}
          href={item.link}
          linkProps={item.linkProps}
          className={className}
          active={isItemActive}
        >
          {item.title}
        </Breadcrumb.Item>
      );
    })}
  </Breadcrumb>
);

export { BreadcrumbHeader, BreadcrumbHeaderProps };
