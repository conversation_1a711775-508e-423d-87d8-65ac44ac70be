import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import BottomButton from '../../../src/components/Stepper/BottomButton';

// style-guide/test/components/Stepper/BottomButton.test.tsx

describe('BottomButton', () => {
  it('renders a single button', () => {
    const config = [{ title: 'Test', testID: 'btn1' }];
    const { getByTestId } = render(<BottomButton buttons={config} />);
    expect(getByTestId('btn1')).toBeInTheDocument();
    expect(getByTestId('btn1')).toHaveTextContent('Test');
  });

  it('renders multiple buttons', () => {
    const config = [
      { title: 'Btn1', testID: 'btn1' },
      { title: 'Btn2', testID: 'btn2' },
    ];
    const { getByTestId } = render(<BottomButton buttons={config} />);
    expect(getByTestId('btn1')).toBeInTheDocument();
    expect(getByTestId('btn2')).toBeInTheDocument();
  });

  it('calls onClick handler', () => {
    const onClick = jest.fn();
    const config = [{ title: 'ClickMe', testID: 'btn', onClick }];
    const { getByTestId } = render(<BottomButton buttons={config} />);
    fireEvent.click(getByTestId('btn'));
    expect(onClick).toHaveBeenCalled();
  });

  it('applies disabled state', () => {
    const config = [{ title: 'Disabled', testID: 'btn', disabled: true }];
    const { getByTestId } = render(<BottomButton buttons={config} />);
    expect(getByTestId('btn')).toBeDisabled();
  });

  it('applies custom class and container class', () => {
    const config = [{ title: 'Custom', testID: 'btn', customClass: 'my-class' }];
    const { getByTestId, container } = render(
      <BottomButton buttons={config} customContainerClass="container-class" />
    );
    expect(getByTestId('btn').className).toMatch(/my-class/);
    // expect(container.firstChild?.className).toMatch(/container-class/);
  });
});