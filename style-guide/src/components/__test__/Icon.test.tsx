import React from 'react';
import { render, screen } from '@testing-library/react';
import Icon from '../Icon';

jest.mock('react-icomoon', () => (props: any) => <svg data-testid="icomoon" {...props} />);
jest.mock('../../selection.json', () => ({}));

describe('Icon', () => {
  it('renders with required icon prop', () => {
    render(<Icon icon="home" />);
    expect(screen.getByTestId('icomoon')).toBeInTheDocument();
  });

  it('applies className and size', () => {
    render(<Icon icon="user" className="custom-class" size={32} />);
    const span = screen.getByTestId('icomoon').parentElement;
    expect(span).toHaveClass('paris2-icon');
    expect(span).toHaveClass('custom-class');
  });

  it('passes color prop to IcoMoon', () => {
    render(<Icon icon="star" color="#ff0" />);
    expect(screen.getByTestId('icomoon')).toHaveAttribute('color', '#ff0');
  });

  it('applies custom style', () => {
    render(<Icon icon="star" style={{ background: 'red' }} />);
    const span = screen.getByTestId('icomoon').parentElement;
    expect(span).toHaveStyle('background: red');
  });
});
