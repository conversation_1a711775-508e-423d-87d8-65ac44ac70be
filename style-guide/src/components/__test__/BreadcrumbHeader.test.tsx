import React from 'react';
import { render, screen } from '@testing-library/react';
import { BreadcrumbHeader, BreadcrumbHeaderProps } from '../BreadcrumbHeader';

const items = [
  { title: 'Home', link: '/' },
  { title: 'Section', link: '/section' },
  { title: 'Current', link: '/current' },
];

describe('BreadcrumbHeader', () => {
  it('renders all breadcrumb items', () => {
    render(<BreadcrumbHeader items={items} activeItem="Current" />);
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Section')).toBeInTheDocument();
    expect(screen.getByText('Current')).toBeInTheDocument();
  });

  it('applies active class to the active item', () => {
    render(<BreadcrumbHeader items={items} activeItem="Current" />);
    const active = screen.getByText('Current');
    expect(active).toHaveClass('breadcrumb-text');
  });

  it('applies inactive class to non-active items', () => {
    render(<BreadcrumbHeader items={items} activeItem="Current" />);
    const inactive = screen.getByText('Home');
    // react-bootstrap may wrap the text in a <span> or <a>, so check the closest parent
    const parent = inactive.closest('.inactive-breadcrumb-text') || inactive.parentElement;
    expect(parent).toHaveClass('inactive-breadcrumb-text');
  });

  it('renders with linkAs prop', () => {
    const Link = (props: any) => <a data-testid="custom-link" {...props} />;
    render(<BreadcrumbHeader items={items} activeItem="Current" linkAs={Link} />);
    // Only non-active items are rendered as links
    expect(screen.getAllByTestId('custom-link').length).toBe(items.length - 1);
  });
});
