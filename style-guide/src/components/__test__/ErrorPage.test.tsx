import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import ErrorPage from '../ErrorPage';

jest.mock('../../user-service', () => ({
  __esModule: true,
  default: {
    init: jest.fn().mockResolvedValue({ keycloak: { tokenParsed: { azure_ad_upn: '<EMAIL>' } } }),
  },
}));

jest.mock('../../assets/images/maintenance.png', () => 'mocked-image-path');

describe('ErrorPage', () => {
  it('renders 404 error', () => {
    render(<ErrorPage errorCode={404} />);
    expect(screen.getByText('404')).toBeInTheDocument();
    expect(screen.getByText('Page not found')).toBeInTheDocument();
    expect(screen.getByText('The page you are trying to find is not available.')).toBeInTheDocument();
  });

  it('renders 403 error and shows access link if Azure AD UPN is present', async () => {
    render(<ErrorPage errorCode={403} />);
    expect(screen.getByText('403')).toBeInTheDocument();
    expect(screen.getByText('Access is denied')).toBeInTheDocument();
    await waitFor(() => {
      expect(screen.getByText('Think this is wrong?')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Click Here To Learn More/i })).toBeInTheDocument();
    });
  });

  it('renders 500 error for unknown code', () => {
    render(<ErrorPage errorCode={500} />);
    expect(screen.getByText('500')).toBeInTheDocument();
    expect(screen.getByText('Something went wrong.')).toBeInTheDocument();
    expect(screen.getByText('Please contact the maintainer')).toBeInTheDocument();
  });

  it('renders custom message if provided', () => {
    render(<ErrorPage errorCode={404} customMessage="Custom error message" />);
    expect(screen.getByText('Custom error message')).toBeInTheDocument();
  });
});
