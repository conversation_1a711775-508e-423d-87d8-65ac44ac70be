import { renderHook, act } from '@testing-library/react';
import * as yup from 'yup';
import { useYupValidationResolver } from '../SendEmailModal/useYupValidationResolver';

describe('useYupValidationResolver', () => {
  const schema = yup.object().shape({
    name: yup.string().required('Name is required'),
    age: yup.number().min(18, 'Must be at least 18').required('Age is required'),
  });

  it('returns values and no errors for valid data', async () => {
    const { result } = renderHook(() => useYupValidationResolver(schema));
    const data = { name: 'John', age: 25 };
    let response: any;
    await act(async () => {
      response = await result.current(data);
    });
    expect(response.values).toEqual(data);
    expect(response.errors).toEqual({});
  });

  it('returns errors for invalid data', async () => {
    const { result } = renderHook(() => useYupValidationResolver(schema));
    const data = { name: '', age: 15 };
    let response: any;
    await act(async () => {
      response = await result.current(data);
    });
    expect(response.values).toEqual({});
    expect(response.errors).toHaveProperty('name');
    expect(response.errors).toHaveProperty('age');
    expect(response.errors.name.message).toBe('Name is required');
    expect(response.errors.age.message).toBe('Must be at least 18');
  });
});
