import React from "react";
import { render, cleanup } from "@testing-library/react";
import "@testing-library/jest-dom";
import Loader from "../../../src/components/Stepper/Loader";

// Mock classnames to return a joined string of class names for easier assertions
jest.mock("classnames", () => (...args: any[]) => {
  // Flatten args and handle objects for conditional classes
  return args
    .flat()
    .map((arg: any) => {
      if (!arg) return "";
      if (typeof arg === "string") return arg;
      if (typeof arg === "object") {
        return Object.entries(arg)
          .filter(([_, value]) => !!value)
          .map(([key]) => key)
          .join(" ");
      }
      return "";
    })
    .filter(Boolean)
    .join(" ");
});

afterEach(cleanup);

describe("Loader Component", () => {
  it("test_loader_renders_with_default_classes_and_structure", () => {
    const { getByTestId } = render(<Loader />);
    const loaderDiv = getByTestId("defaultLoader");
    expect(loaderDiv).toBeInTheDocument();
    expect(loaderDiv).toHaveClass("border-5");
    expect(loaderDiv).toHaveClass("primary-txt-color");
    const outerDiv = loaderDiv.parentElement;
    expect(outerDiv).toHaveClass("w-full");
    expect(outerDiv).toHaveClass("h-full");
    expect(outerDiv).toHaveClass("flex-1");
    expect(outerDiv).toHaveClass("d-flex");
    expect(outerDiv).toHaveClass("justify-content-center");
    expect(outerDiv).toHaveClass("align-items-center");
  });

  it("test_loader_applies_custom_classname", () => {
    const customClass = "my-custom-class";
    const { getByTestId } = render(<Loader className={customClass} />);
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv).toHaveClass(customClass);
  });

  it("test_loader_adds_overlay_class_when_isOverlayLoader_true", () => {
    const { getByTestId } = render(<Loader isOverlayLoader={true} />);
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv).toHaveClass("stepper-overlay-loader");
  });

  it("test_loader_does_not_add_overlay_class_when_isOverlayLoader_false", () => {
    const { getByTestId } = render(<Loader isOverlayLoader={false} />);
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv).not.toHaveClass("stepper-overlay-loader");
  });

  it("test_loader_handles_undefined_or_null_classname", () => {
    const { getByTestId: getByTestIdUndefined, unmount } = render(
      <Loader className={undefined} />
    );
    const outerDivUndefined = getByTestIdUndefined("defaultLoader")
      .parentElement;
    expect(outerDivUndefined).not.toHaveClass("undefined");
    unmount();

    const { getByTestId: getByTestIdNull } = render(
      <Loader className={null as any} />
    );
    const outerDivNull = getByTestIdNull("defaultLoader").parentElement;
    expect(outerDivNull).not.toHaveClass("null");
  });

  it("test_loader_renders_with_both_classname_and_isOverlayLoader", () => {
    const customClass = "custom-class";
    const { getByTestId } = render(
      <Loader className={customClass} isOverlayLoader={true} />
    );
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv).toHaveClass(customClass);
    expect(outerDiv).toHaveClass("stepper-overlay-loader");
  });

  // Additional tests

  it("test_loader_renders_without_any_props", () => {
    const { getByTestId } = render(<Loader />);
    const loaderDiv = getByTestId("defaultLoader");
    expect(loaderDiv).toBeInTheDocument();
    const outerDiv = loaderDiv.parentElement;
    expect(outerDiv).not.toHaveClass("stepper-overlay-loader");
  });

  it("test_loader_renders_with_multiple_custom_classes", () => {
    const customClass = "foo bar baz";
    const { getByTestId } = render(<Loader className={customClass} />);
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv).toHaveClass("foo");
    expect(outerDiv).toHaveClass("bar");
    expect(outerDiv).toHaveClass("baz");
  });

  it("test_loader_renders_with_isOverlayLoader_and_custom_class", () => {
    const customClass = "overlay-custom";
    const { getByTestId } = render(
      <Loader className={customClass} isOverlayLoader />
    );
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv).toHaveClass("overlay-custom");
    expect(outerDiv).toHaveClass("stepper-overlay-loader");
  });

  it("test_loader_renders_with_falsy_classname", () => {
    const { getByTestId } = render(<Loader className={""} />);
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv?.className).not.toContain("undefined");
    expect(outerDiv?.className).not.toContain("null");
  });

  it("test_loader_renders_with_boolean_classname", () => {
    const { getByTestId } = render(<Loader className={false as any} />);
    const outerDiv = getByTestId("defaultLoader").parentElement;
    expect(outerDiv?.className).not.toContain("false");
  });
});
