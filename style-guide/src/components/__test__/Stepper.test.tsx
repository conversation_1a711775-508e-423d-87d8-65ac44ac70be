beforeAll(() => {
  // @ts-ignore
  HTMLElement.prototype.scrollTo = function() {};
});

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import Stepper from "../Stepper";

const steps = [
  {
    label: "Step 1",
    component: <div data-testid="step-1">Step 1 Content</div>,
  },
  {
    label: "Step 2",
    component: <div data-testid="step-2">Step 2 Content</div>,
    validate: jest.fn().mockResolvedValue(true),
  },
  {
    label: "Step 3",
    component: <div data-testid="step-3">Step 3 Content</div>,
  },
];

describe("Stepper", () => {
  it("renders the first step by default", () => {
    render(
      <Stepper
        steps={steps}
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
      />
    );
    expect(screen.getByTestId("step-1")).toBeInTheDocument();
  });

  it("renders the correct step when defaultLoadStep is provided", () => {
    render(
      <Stepper
        steps={steps}
        defaultLoadStep={2}
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
      />
    );
    expect(screen.getByTestId("step-2")).toBeInTheDocument();
  });

  it("calls onStepChange when step changes via stepper navigation", async () => {
    const onStepChange = jest.fn();
    render(
      <Stepper
        steps={steps}
        onStepChange={onStepChange}
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
      />
    );
    // Move to step 2 first
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
    await waitFor(() => screen.getByTestId("step-2"));
    // Now, Step 1 card is clickable (completed)
    const step1Cards = document.querySelectorAll(".stepper-card-completed");
    expect(step1Cards.length).toBeGreaterThan(0);
    fireEvent.click(step1Cards[0]);
    await waitFor(() => expect(onStepChange).toHaveBeenCalledWith(1, 2));
  });

  it("calls onNext when moving to next step", async () => {
    const onNext = jest.fn();
    render(
      <Stepper
        steps={steps}
        onNext={onNext}
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
      />
    );
    const nextBtn = screen.getByTestId("stepper-prim-btn");
    fireEvent.click(nextBtn);
    await waitFor(() => expect(onNext).toHaveBeenCalledWith(1));
  });

  it("calls primaryBtnOnClick on last step", async () => {
    const primaryBtnOnClick = jest.fn();
    render(
      <Stepper
        steps={steps}
        defaultLoadStep={3}
        primaryBtnOnClick={primaryBtnOnClick}
        primaryBtnTitle={"Next"}
      />
    );
    const nextBtn = screen.getByTestId("stepper-prim-btn");
    fireEvent.click(nextBtn);
    await waitFor(() => expect(primaryBtnOnClick).toHaveBeenCalled());
  });

  it("calls secondaryBtnOnClick with current step", () => {
    const secondaryBtnOnClick = jest.fn();
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        secondaryBtnOnClick={secondaryBtnOnClick}
      />
    );
    const secBtn = screen.getByTestId("stepper-sec-btn");
    fireEvent.click(secBtn);
    expect(secondaryBtnOnClick).toHaveBeenCalledWith(1);
  });

  it("calls onClose when close button is clicked", () => {
    const onClose = jest.fn();
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        breadCrumbTitle="Test Breadcrumb"
        onClose={onClose}
        testIds={{ closeBtn: "close-btn" }}
      />
    );
    fireEvent.click(screen.getByTestId("close-btn"));
    expect(onClose).toHaveBeenCalled();
  });

  it("shows loader overlay when loading and showLoader is true", async () => {
    const validate = jest
      .fn()
      .mockImplementation(
        () => new Promise((res) => setTimeout(() => res(true), 100))
      );
    const customSteps = [
      { label: "Step 1", component: <div>1</div> },
      { label: "Step 2", component: <div>2</div>, validate },
    ];
    const { container } = render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={customSteps}
        showLoader
      />
    );
    const nextBtn = screen.getByTestId("stepper-prim-btn");
    fireEvent.click(nextBtn); // Go to step 2
    await waitFor(() => screen.getByTestId("stepper-prim-btn"));
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
    // Loader overlay should be present
    expect(screen.getByTestId("defaultLoader")).toBeInTheDocument();
    // Optionally, check for overlay class
    expect(
      container.querySelector(".stepper-overlay-loader")
    ).toBeInTheDocument();
  });

  it("does not advance if validate returns false", async () => {
    const validate = jest.fn().mockResolvedValue(false);
    const customSteps = [
      {
        label: "Step 1",
        component: <div data-testid="step-1">Step 1 Content</div>,
        validate,
      },
      {
        label: "Step 2",
        component: <div data-testid="step-2">Step 2 Content</div>,
      },
    ];
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={customSteps}
        showLoader
      />
    );
    const nextBtn = screen.getByTestId("stepper-prim-btn");
    fireEvent.click(nextBtn);
    // Wait for validate to be called
    await waitFor(() => expect(validate).toHaveBeenCalled());
    // Should still be on step 1
    expect(screen.getByTestId("step-1")).toBeInTheDocument();
    // Loader should not be present after validation fails
    expect(screen.queryByTestId("defaultLoader")).not.toBeInTheDocument();
  });

  it("renders previewComponent in preview mode", () => {
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        isPreview
        previewComponent={<div data-testid="preview">Preview Content</div>}
      />
    );
    expect(screen.getByTestId("preview")).toBeInTheDocument();
  });

  it("renders custom stepper if provided", () => {
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        customStepper={<div data-testid="custom-stepper">Custom Stepper</div>}
      />
    );
    expect(screen.getByTestId("custom-stepper")).toBeInTheDocument();
  });

  it("triggers scrollTo and onStepChange when step changes in preview mode", async () => {
    const onStepChange = jest.fn();
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        isPreview={true}
        onStepChange={onStepChange}
        previewComponent={<div data-testid="preview">Preview Content</div>}
      />
    );
    // Simulate step change in preview mode
    // We need to get the Stepper instance and call handleStepsChange, but since it's not exposed,
    // we simulate by changing props (not possible here), so instead, we just check that preview renders
    expect(screen.getByTestId("preview")).toBeInTheDocument();
    // This test is mainly to trigger the useEffect with isPreview dependency
  });

  it("does not call onStepChange if not provided", async () => {
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
      />
    );
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
    await waitFor(() => screen.getByTestId("step-2"));
    // No error should occur, and step should advance
    expect(screen.getByTestId("step-2")).toBeInTheDocument();
  });

  it("toggles preview mode and triggers useEffect", async () => {
    const { rerender } = render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        isPreview={false}
        previewComponent={<div data-testid="preview">Preview Content</div>}
      />
    );
    expect(screen.queryByTestId("preview")).not.toBeInTheDocument();
    rerender(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        isPreview={true}
        previewComponent={<div data-testid="preview">Preview Content</div>}
      />
    );
    expect(screen.getByTestId("preview")).toBeInTheDocument();
  });

  it("disables primary and secondary buttons when props are set", () => {
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        primaryBtnDisabled
        secondaryBtnDisabled
      />
    );
    expect(screen.getByTestId("stepper-prim-btn")).toBeDisabled();
    expect(screen.getByTestId("stepper-sec-btn")).toBeDisabled();
  });

  it("renders additional buttons", () => {
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        additionalButtons={[
          {
            title: "Extra",
            testID: "extra-btn",
            onClick: jest.fn(),
          },
        ]}
      />
    );
    expect(screen.getByTestId("extra-btn")).toBeInTheDocument();
  });
  it("calls onStepChange when step changes via stepper navigation", async () => {
    const onStepChange = jest.fn();
    render(
      <Stepper
        primaryBtnTitle={"Next"}
        steps={steps}
        primaryBtnOnClick={async () => Promise.resolve()}
        onStepChange={onStepChange}
      />
    );
    // Move to step 2
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
    await waitFor(() => screen.getByTestId("step-2"));
    // Move to step 3
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
    await waitFor(() => screen.getByTestId("step-3"));
    // Check onStepChange calls
    expect(onStepChange).toHaveBeenCalledWith(2, 1);
    expect(onStepChange).toHaveBeenCalledWith(3, 2);
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
  });
  it("renders preview if provided", () => {
    render(
      <Stepper
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={"Next"}
        steps={steps}
        isPreview
        previewComponent={<div data-testid="preview">Preview Content</div>}
      />
    );
    expect(screen.getByTestId("preview")).toBeInTheDocument();
  });
  it("renders custom titel for primary button if provided", async () => {
    const onTitleChange = jest.fn();
    render(
      <Stepper
        steps={steps}
        primaryBtnOnClick={() => Promise.resolve()}
        primaryBtnTitle={onTitleChange}
      />
    );
    expect(onTitleChange).toHaveBeenCalledWith(1, 3);
    // Move to step 2
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
    await waitFor(() => screen.getByTestId("step-2"));
    // Move to step 3
    fireEvent.click(screen.getByTestId("stepper-prim-btn"));
    await waitFor(() => screen.getByTestId("step-3"));
  });
});
