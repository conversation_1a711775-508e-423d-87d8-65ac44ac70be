import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import BarChart from '../../DashboardWidget/common/BarChart';

// Mock the D3 chart hook
jest.mock('../../DashboardWidget/hooks/useD3Chart', () => ({
  useD3Chart: () => ({
    xScale: jest.fn((value) => value * 10),
    yScale: Object.assign(
      jest.fn((name) => 20 + (name === 'Test Vessel 1_0' ? 0 : 40)),
      { bandwidth: () => 30 }
    ),
    barColorScale: jest.fn(() => '#ff0000'),
    textColorScale: jest.fn(() => '#ffffff'),
    stackedBarData: [
      {
        vesselName: 'Test Vessel 1',
        uniqueId: 'Test Vessel 1_0',
        segments: [
          {
            key: 'Status A',
            value: 10,
            x: 0,
            width: 30, // Fixed width
            y: 20,
            height: 30,
            vesselName: 'Test Vessel 1',
            uniqueId: 'Test Vessel 1_0',
          },
        ],
      },
      {
        vesselName: 'Test Vessel 1', // Duplicate name
        uniqueId: 'Test Vessel 1_1', // Different uniqueId
        segments: [
          {
            key: 'Status B',
            value: 15,
            x: 0,
            width: 30, // Fixed width
            y: 60,
            height: 30,
            vesselName: 'Test Vessel 1',
            uniqueId: 'Test Vessel 1_1',
          },
        ],
      },
    ],
    chartHeight: 100,
    totalHeight: 170,
  }),
}));

// Mock ChartComponents to avoid d3 scale issues
jest.mock('../../DashboardWidget/common/ChartComponents', () => ({
  ScrollableChartElements: () => <g data-testid="scrollable-chart-elements" />,
  StickyXAxis: () => <g data-testid="sticky-x-axis" />,
  ChartTooltip: ({ isVisible, content }: any) =>
    isVisible ? <div data-testid="chart-tooltip" dangerouslySetInnerHTML={{ __html: content }} /> : null,
  ChartLegend: ({ valueHeaders }: any) => (
    <div className="xAxix-legend-parent" data-testid="chart-legend">
      {valueHeaders.map((header: string) => (
        <span key={header}>{header}</span>
      ))}
    </div>
  ),
}));

// Mock react-router-dom
const mockPush = jest.fn();
jest.mock('react-router-dom', () => ({
  BrowserRouter: ({ children }: any) => <div data-testid="router">{children}</div>,
  useHistory: () => ({
    push: mockPush,
  }),
}));

const mockProps = {
  vessels: [
    { name: 'Test Vessel 1', uniqueId: 'Test Vessel 1_0', 'Status A': 10 },
    { name: 'Test Vessel 1', uniqueId: 'Test Vessel 1_1', 'Status B': 15 },
  ],
  valueHeaders: ['Status A', 'Status B'],
  badgeColors: ['#ff0000', '#00ff00'],
  valueDomain: [0, 25] as [number, number],
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
};

const BarChartWithRouter = (props: any) => (
  <BrowserRouter>
    <BarChart {...props} />
  </BrowserRouter>
);

describe('BarChart Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders bars with fixed 30px width', () => {
    const { container } = render(<BarChartWithRouter {...mockProps} />);
    const rects = container.querySelectorAll('rect');
    
    // Check that bars have fixed width of 30px
    rects.forEach((rect) => {
      expect(rect.getAttribute('width')).toBe('30');
    });
  });

  test('handles duplicate vessel names with unique IDs', () => {
    const { container } = render(<BarChartWithRouter {...mockProps} />);
    const barGroups = container.querySelectorAll('g[transform*="translate"]');
    
    // Should have 2 separate bar groups for duplicate names
    expect(barGroups.length).toBe(2);
  });

  test('displays correct values in bars', () => {
    render(<BarChartWithRouter {...mockProps} />);
    
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('15')).toBeInTheDocument();
  });

  test('handles click events correctly', () => {
    const { container } = render(<BarChartWithRouter {...mockProps} />);
    const barSegment = container.querySelector('.bar-subgroup');

    if (barSegment) {
      fireEvent.click(barSegment);
      // The click handler should be called (we can't easily test the exact URL without more complex mocking)
      expect(barSegment).toBeInTheDocument();
    }
  });

  test('shows tooltip on mouse over', () => {
    const { container } = render(<BarChartWithRouter {...mockProps} />);
    const barSegment = container.querySelector('.bar-subgroup');
    
    if (barSegment) {
      fireEvent.mouseOver(barSegment);
      // Tooltip content is set in state, we can check if the event was handled
      expect(barSegment).toBeInTheDocument();
    }
  });

  test('renders legend and axis components', () => {
    render(<BarChartWithRouter {...mockProps} />);

    // Check for mocked components
    expect(screen.getByTestId('chart-legend')).toBeInTheDocument();
    expect(screen.getByTestId('scrollable-chart-elements')).toBeInTheDocument();
    expect(screen.getByTestId('sticky-x-axis')).toBeInTheDocument();
  });

  test('uses dynamic value domain', () => {
    // The valueDomain prop should be calculated dynamically
    expect(mockProps.valueDomain[0]).toBe(0);
    expect(mockProps.valueDomain[1]).toBe(25); // Max value with buffer
  });
});
