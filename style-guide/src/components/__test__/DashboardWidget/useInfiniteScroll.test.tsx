import { renderHook, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useInfiniteScroll } from '../../DashboardWidget/hooks/useInfiniteScroll';

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe('useInfiniteScroll Hook', () => {
  const mockFetchNextPage = jest.fn();

  const defaultProps = {
    fetchNextPage: mockFetchNextPage,
    isFetchingNextPage: false,
    hasNextPage: true,
    dataLength: 10,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('returns containerRef and handleScroll function', () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    expect(result.current.containerRef).toBeDefined();
    expect(result.current.handleScroll).toBeDefined();
    expect(typeof result.current.handleScroll).toBe('function');
  });

  test('does not fetch when already fetching', () => {
    const props = {
      ...defaultProps,
      isFetchingNextPage: true,
    };

    const { result } = renderHook(() => useInfiniteScroll(props));

    // Simulate scroll event
    act(() => {
      const mockEvent = {
        currentTarget: {
          scrollTop: 100,
          scrollHeight: 200,
          clientHeight: 50,
        },
      } as any;
      result.current.handleScroll(mockEvent);
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test('does not fetch when no next page available', () => {
    const props = {
      ...defaultProps,
      hasNextPage: false,
    };

    const { result } = renderHook(() => useInfiniteScroll(props));

    act(() => {
      const mockEvent = {
        currentTarget: {
          scrollTop: 100,
          scrollHeight: 200,
          clientHeight: 50,
        },
      } as any;
      result.current.handleScroll(mockEvent);
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test('fetches next page when scrolled near bottom', () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    act(() => {
      const mockEvent = {
        currentTarget: {
          scrollTop: 140, // Near bottom (200 - 50 - 10 threshold)
          scrollHeight: 200,
          clientHeight: 50,
        },
      } as any;
      result.current.handleScroll(mockEvent);
    });

    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);
  });

  test('does not fetch when not scrolled enough', () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    act(() => {
      const mockEvent = {
        currentTarget: {
          scrollTop: 50, // Not near bottom
          scrollHeight: 200,
          clientHeight: 50,
        },
      } as any;
      result.current.handleScroll(mockEvent);
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test('handles scroll threshold correctly', () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    // Test exactly at threshold
    act(() => {
      const mockEvent = {
        currentTarget: {
          scrollTop: 140, // scrollHeight (200) - clientHeight (50) - threshold (10) = 140
          scrollHeight: 200,
          clientHeight: 50,
        },
      } as any;
      result.current.handleScroll(mockEvent);
    });

    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);
  });

  test('updates when props change', () => {
    const { result, rerender } = renderHook(
      (props) => useInfiniteScroll(props),
      { initialProps: defaultProps }
    );

    // Initially should work
    act(() => {
      const mockEvent = {
        currentTarget: {
          scrollTop: 140,
          scrollHeight: 200,
          clientHeight: 50,
        },
      } as any;
      result.current.handleScroll(mockEvent);
    });

    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);

    // Update props to disable fetching
    rerender({
      ...defaultProps,
      hasNextPage: false,
    });

    // Should not fetch anymore
    act(() => {
      const mockEvent = {
        currentTarget: {
          scrollTop: 140,
          scrollHeight: 200,
          clientHeight: 50,
        },
      } as any;
      result.current.handleScroll(mockEvent);
    });

    // Should still be 1 (not called again)
    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);
  });
});
