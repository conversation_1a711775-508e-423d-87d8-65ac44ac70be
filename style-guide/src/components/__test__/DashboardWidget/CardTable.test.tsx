import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CardTable from '../../DashboardWidget/iCard/CardTable';

// Mock InfiniteScrollTable
jest.mock('../../DashboardWidget/iCard/InfiniteScrollTable', () => {
  return function MockInfiniteScrollTable(props: any) {
    return (
      <div data-testid="infinite-scroll-table">
        <div data-testid="vessels-count">{props.data.length}</div>
        <div data-testid="columns-count">{props.columns.length}</div>
        {props.data.map((vessel: any, index: number) => (
          <div key={index} data-testid={`vessel-${index}`}>
            {vessel.name}
          </div>
        ))}
        {props.isFetchingNextPage && <div data-testid="loading">Loading...</div>}
      </div>
    );
  };
});

const mockColumns = [
  {
    accessorKey: 'name',
    header: 'Vessel Name',
    cell: ({ getValue }: any) => getValue(),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ getValue }: any) => getValue(),
  },
];

const mockProps = {
  vessels: [
    { name: 'Vessel A', status: 'Active', vesselData: [5, 10] },
    { name: 'Vessel B', status: 'Inactive', vesselData: [8, 12] },
    { name: 'Vessel C', status: 'Active', vesselData: [3, 7] },
  ],
  columns: mockColumns,
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
  pagination: { page: 1, totalPages: 3 },
  isFetchingNextPage: false,
  isLoading: false,
  fetchNextPage: jest.fn(),
};

const CardTableWithRouter = (props: any) => (
  <BrowserRouter>
    <CardTable {...props} />
  </BrowserRouter>
);

describe('CardTable Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<CardTableWithRouter {...mockProps} />);
    expect(screen.getByTestId('infinite-scroll-table')).toBeInTheDocument();
  });

  test('passes correct number of vessels to table', () => {
    render(<CardTableWithRouter {...mockProps} />);
    expect(screen.getByTestId('vessels-count')).toHaveTextContent('3');
  });

  test('passes correct number of columns to table', () => {
    render(<CardTableWithRouter {...mockProps} />);
    expect(screen.getByTestId('columns-count')).toHaveTextContent('2');
  });

  test('renders all vessel names', () => {
    render(<CardTableWithRouter {...mockProps} />);
    
    expect(screen.getByTestId('vessel-0')).toHaveTextContent('Vessel A');
    expect(screen.getByTestId('vessel-1')).toHaveTextContent('Vessel B');
    expect(screen.getByTestId('vessel-2')).toHaveTextContent('Vessel C');
  });

  test('shows loading indicator when fetching next page', () => {
    const loadingProps = {
      ...mockProps,
      isFetchingNextPage: true,
    };

    render(<CardTableWithRouter {...loadingProps} />);
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  test('handles empty vessels array', () => {
    const emptyProps = {
      ...mockProps,
      vessels: [],
    };

    render(<CardTableWithRouter {...emptyProps} />);
    expect(screen.getByTestId('vessels-count')).toHaveTextContent('0');
  });

  test('passes pagination props correctly', () => {
    render(<CardTableWithRouter {...mockProps} />);
    
    // The InfiniteScrollTable should receive the pagination data
    expect(screen.getByTestId('infinite-scroll-table')).toBeInTheDocument();
  });

  test('passes callback functions correctly', () => {
    render(<CardTableWithRouter {...mockProps} />);
    
    // Verify that the component renders (callbacks are passed internally)
    expect(screen.getByTestId('infinite-scroll-table')).toBeInTheDocument();
  });

  test('handles loading state', () => {
    const loadingProps = {
      ...mockProps,
      isLoading: true,
    };

    render(<CardTableWithRouter {...loadingProps} />);
    
    // Component should still render the table structure
    expect(screen.getByTestId('infinite-scroll-table')).toBeInTheDocument();
  });

  test('handles different column configurations', () => {
    const customColumns = [
      {
        accessorKey: 'name',
        header: 'Name',
        cell: ({ getValue }: any) => getValue(),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ getValue }: any) => getValue(),
      },
      {
        accessorKey: 'location',
        header: 'Location',
        cell: ({ getValue }: any) => getValue() || 'N/A',
      },
    ];

    const customProps = {
      ...mockProps,
      columns: customColumns,
    };

    render(<CardTableWithRouter {...customProps} />);
    expect(screen.getByTestId('columns-count')).toHaveTextContent('3');
  });

  test('maintains vessel data structure', () => {
    render(<CardTableWithRouter {...mockProps} />);
    
    // Verify that all vessels are rendered
    expect(screen.getByTestId('vessel-0')).toBeInTheDocument();
    expect(screen.getByTestId('vessel-1')).toBeInTheDocument();
    expect(screen.getByTestId('vessel-2')).toBeInTheDocument();
  });
});
