import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CardModule from '../../DashboardWidget/iCard/CardModule';

// Mock child components
jest.mock('../../DashboardWidget/iCard/CardTable', () => {
  return function MockCardTable(props: any) {
    return <div data-testid="card-table">Table View - {props.vessels.length} vessels</div>;
  };
});

jest.mock('../../DashboardWidget/iCard/CardGrid', () => {
  return function MockCardGrid(props: any) {
    return <div data-testid="card-grid">Grid View - {props.vessels.length} vessels</div>;
  };
});

jest.mock('../../DashboardWidget/iCard/CardModuleHeader', () => ({
  CardModuleHeader: (props: any) => (
    <div data-testid="card-module-header">
      <button onClick={() => props.onViewModeChange('list')}>List</button>
      <button onClick={() => props.onViewModeChange('grid')}>Grid</button>
      <button onClick={props.onToggleModal}>Toggle Modal</button>
    </div>
  ),
}));

jest.mock('../../DashboardWidget/iCard/CardDropdownSelectors', () => ({
  CardDropdownSelectors: () => <div data-testid="card-dropdown-selectors">Dropdowns</div>,
}));

jest.mock('../../DashboardWidget/iCard/ModuleModal', () => ({
  ModuleModal: ({ children, isOpen }: any) => 
    isOpen ? <div data-testid="module-modal">{children}</div> : null,
}));

jest.mock('../../DashboardWidget/iCard/CardTabs', () => ({
  CardTabs: (props: any) => (
    <div data-testid="card-tabs">
      {props.tabs.map((tab: string) => (
        <button key={tab} onClick={() => props.onTabChange(tab)}>
          {tab}
        </button>
      ))}
    </div>
  ),
}));

const mockProps = {
  title: 'Test Dashboard',
  vessels: [
    { name: 'Vessel A', status: 'Active', vesselData: [5, 10] },
    { name: 'Vessel B', status: 'Inactive', vesselData: [8, 12] },
  ],
  staticData: {
    tabs: ['Active', 'Inactive', 'All'],
    tableHeaders: ['Vessel', 'Status A', 'Status B', 'Action'],
    badgeColors: ['#ff0000', '#00ff00'],
  },
  visibleConfig: {
    IsAllTabVisible: true,
    IsLastUpdatedVisible: true,
    IsRefereshIconVisible: true,
    IsDropdownVisible: true,
    IsTabsVisible: true,
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
  },
  multiVesselSelects: [],
  componentView: {
    defaultComponent: 'list' as const,
    gridComponent: 'bar' as const,
  },
  sizeKey: 'md' as const,
  onRefresh: jest.fn(),
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
  fetchNextPage: jest.fn(),
  isFetchingNextPage: false,
  isLoading: false,
  pagination: { page: 1, totalPages: 1 },
  columns: [],
};

const CardModuleWithRouter = (props: any) => (
  <BrowserRouter>
    <CardModule {...props} />
  </BrowserRouter>
);

describe('CardModule Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    expect(screen.getByTestId('card-module-header')).toBeInTheDocument();
  });

  test('renders in list view by default', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    expect(screen.getByTestId('card-table')).toBeInTheDocument();
    expect(screen.queryByTestId('card-grid')).not.toBeInTheDocument();
  });

  test('switches to grid view when button is clicked', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    fireEvent.click(screen.getByText('Grid'));
    
    expect(screen.getByTestId('card-grid')).toBeInTheDocument();
    expect(screen.queryByTestId('card-table')).not.toBeInTheDocument();
  });

  test('switches back to list view', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    // Switch to grid first
    fireEvent.click(screen.getByText('Grid'));
    expect(screen.getByTestId('card-grid')).toBeInTheDocument();
    
    // Switch back to list
    fireEvent.click(screen.getByText('List'));
    expect(screen.getByTestId('card-table')).toBeInTheDocument();
    expect(screen.queryByTestId('card-grid')).not.toBeInTheDocument();
  });

  test('opens modal when toggle button is clicked', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    fireEvent.click(screen.getByText('Toggle Modal'));
    
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();
  });

  test('renders with grid view as default when specified', () => {
    const gridProps = {
      ...mockProps,
      componentView: {
        defaultComponent: 'grid' as const,
        gridComponent: 'bar' as const,
      },
    };
    
    render(<CardModuleWithRouter {...gridProps} />);
    expect(screen.getByTestId('card-grid')).toBeInTheDocument();
  });

  test('handles refresh callback', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    // This would be triggered by the refresh icon in the header
    expect(mockProps.onRefresh).not.toHaveBeenCalled();
  });

  test('passes correct props to child components', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    // Check that vessels are passed correctly
    expect(screen.getByText('Table View - 2 vessels')).toBeInTheDocument();
  });
});
