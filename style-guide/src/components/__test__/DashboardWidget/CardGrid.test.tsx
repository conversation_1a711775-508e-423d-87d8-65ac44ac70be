import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CardGrid from '../../DashboardWidget/iCard/CardGrid';

// Mock the BarChart component
jest.mock('../../DashboardWidget/common/BarChart', () => {
  return function MockBarChart(props: any) {
    return (
      <div data-testid="bar-chart">
        <div data-testid="value-domain">{JSON.stringify(props.valueDomain)}</div>
        <div data-testid="vessels-count">{props.vessels.length}</div>
        <div data-testid="unique-ids">
          {props.vessels.map((v: any) => v.uniqueId).join(',')}
        </div>
        <div data-testid="value-headers">{props.valueHeaders.join(',')}</div>
      </div>
    );
  };
});

// Mock other components
jest.mock('../../DashboardWidget/common/DonutChartAdapter', () => () => 
  <div data-testid="donut-chart">DonutChart</div>
);
jest.mock('../../DashboardWidget/common/DashboardGrid/Dashboard', () => () => 
  <div data-testid="dashboard">Dashboard</div>
);
jest.mock('../../DashboardWidget/iCard/Spinner', () => () => 
  <div data-testid="spinner">Spinner</div>
);

// Mock hooks
jest.mock('../../DashboardWidget/hooks/useInfiniteScroll', () => ({
  useInfiniteScroll: () => ({
    containerRef: { current: null },
    handleScroll: jest.fn(),
  }),
}));

const mockProps = {
  vessels: [
    {
      name: 'Vessel A',
      vesselData: [5, 10], // Status A: 5, Status B: 10, total: 15
    },
    {
      name: 'Vessel B',
      vesselData: [8, 12], // Status A: 8, Status B: 12, total: 20
    },
    {
      name: 'Vessel A', // Duplicate name
      vesselData: [3, 7], // Status A: 3, Status B: 7, total: 10
    },
  ],
  tableHeaders: ['Vessel', 'Status A', 'Status B', 'Action'],
  badgeColors: ['#ff0000', '#00ff00'],
  isFetchingNextPage: false,
  isLoading: false,
  fetchNextPage: jest.fn(),
  pagination: { page: 1, totalPages: 1 },
  gridComponent: 'bar' as const,
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
};

const CardGridWithRouter = (props: any) => (
  <BrowserRouter>
    <CardGrid {...props} />
  </BrowserRouter>
);

describe('CardGrid Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<CardGridWithRouter {...mockProps} />);
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
  });

  test('calculates dynamic valueDomain correctly', () => {
    render(<CardGridWithRouter {...mockProps} />);
    
    // Max total value is 20 (Vessel B: 8 + 12)
    // With 1.1 buffer: 20 * 1.1 = 22
    const valueDomainElement = screen.getByTestId('value-domain');
    const valueDomain = JSON.parse(valueDomainElement.textContent || '');
    
    expect(valueDomain[0]).toBe(0);
    expect(valueDomain[1]).toBe(22); // 20 * 1.1
  });

  test('creates unique IDs for duplicate vessel names', () => {
    render(<CardGridWithRouter {...mockProps} />);
    
    const uniqueIdsElement = screen.getByTestId('unique-ids');
    const uniqueIds = uniqueIdsElement.textContent?.split(',') || [];
    
    // Should have 3 unique IDs for 3 vessels
    expect(uniqueIds).toHaveLength(3);
    expect(uniqueIds).toContain('Vessel A_0');
    expect(uniqueIds).toContain('Vessel B_1');
    expect(uniqueIds).toContain('Vessel A_2');
  });

  test('handles vessels with same name separately', () => {
    render(<CardGridWithRouter {...mockProps} />);
    
    const vesselsCountElement = screen.getByTestId('vessels-count');
    expect(vesselsCountElement.textContent).toBe('3'); // All 3 vessels should be processed
  });

  test('filters out non-data headers correctly', () => {
    render(<CardGridWithRouter {...mockProps} />);
    
    const valueHeadersElement = screen.getByTestId('value-headers');
    expect(valueHeadersElement.textContent).toBe('Status A,Status B');
  });

  test('renders loading state', () => {
    render(<CardGridWithRouter {...{ ...mockProps, isLoading: true }} />);
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  test('renders no results message when vessels array is empty', () => {
    render(<CardGridWithRouter {...{ ...mockProps, vessels: [] }} />);
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  test('renders pie chart when gridComponent is pie', () => {
    render(<CardGridWithRouter {...{ ...mockProps, gridComponent: 'pie' }} />);
    expect(screen.getByTestId('donut-chart')).toBeInTheDocument();
  });

  test('renders dashboard when gridComponent is dashboard', () => {
    render(<CardGridWithRouter {...{ ...mockProps, gridComponent: 'dashboard' }} />);
    expect(screen.getByTestId('dashboard')).toBeInTheDocument();
  });

  test('shows loading indicator when fetching next page', () => {
    render(<CardGridWithRouter {...{ ...mockProps, isFetchingNextPage: true }} />);
    expect(screen.getAllByTestId('spinner')).toHaveLength(1);
  });
});
