import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import Spinner from '../../DashboardWidget/iCard/Spinner';

describe('Spinner Component', () => {
  test('renders without crashing', () => {
    const { container } = render(<Spinner />);
    expect(container.firstChild).toBeInTheDocument();
  });

  test('renders as a div element by default', () => {
    const { container } = render(<Spinner />);
    const spinner = container.firstChild as HTMLElement;

    expect(spinner.tagName.toLowerCase()).toBe('div');
  });

  test('is visible when rendered', () => {
    const { container } = render(<Spinner />);
    const spinner = container.firstChild as HTMLElement;

    expect(spinner).toBeVisible();
  });

  test('can be rendered multiple times', () => {
    const { container } = render(
      <div>
        <Spinner />
        <Spinner />
        <Spinner />
      </div>
    );

    // Each Spinner renders a div with a span inside
    const loadingTexts = container.querySelectorAll('span');
    expect(loadingTexts).toHaveLength(3);
    loadingTexts.forEach(span => {
      expect(span).toHaveTextContent('Loading...');
    });
  });

  test('maintains consistent structure', () => {
    const { container: container1 } = render(<Spinner />);
    const { container: container2 } = render(<Spinner />);
    
    const spinner1 = container1.firstChild as HTMLElement;
    const spinner2 = container2.firstChild as HTMLElement;
    
    // Both spinners should have the same tag name
    expect(spinner1.tagName).toBe(spinner2.tagName);
  });

  test('can be used in loading states', () => {
    const LoadingComponent = ({ isLoading }: { isLoading: boolean }) => (
      <div>
        {isLoading ? <Spinner /> : <div>Content loaded</div>}
      </div>
    );

    const { rerender, getByText, container } = render(
      <LoadingComponent isLoading={true} />
    );
    
    // Should show spinner when loading
    expect(container.querySelector('div > div')).toBeInTheDocument();
    
    // Should show content when not loading
    rerender(<LoadingComponent isLoading={false} />);
    expect(getByText('Content loaded')).toBeInTheDocument();
  });

  test('has appropriate accessibility attributes', () => {
    const { container } = render(<Spinner />);
    const spinner = container.firstChild as HTMLElement;
    
    // Check if it has role or aria attributes for accessibility
    // This test might need adjustment based on actual implementation
    expect(spinner).toBeInTheDocument();
  });
});
