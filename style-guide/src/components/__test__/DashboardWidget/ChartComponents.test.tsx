import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock d3 completely
jest.mock('d3', () => ({
  select: jest.fn(() => ({
    call: jest.fn().mockReturnThis(),
    selectAll: jest.fn(() => ({
      call: jest.fn().mockReturnThis(),
      attr: jest.fn().mockReturnThis(),
    })),
  })),
  axisLeft: jest.fn(() => ({
    tickFormat: jest.fn().mockReturnThis(),
    tickSize: jest.fn().mockReturnThis(),
  })),
  axisBottom: jest.fn(() => ({
    tickSize: jest.fn().mockReturnThis(),
    tickFormat: jest.fn().mockReturnThis(),
    tickValues: jest.fn().mockReturnThis(),
  })),
}));

// Mock the actual components to avoid d3 execution
jest.mock('../../DashboardWidget/common/ChartComponents', () => ({
  ScrollableChartElements: (props: any) => (
    <g data-testid="scrollable-chart-elements" className="x-grid y-axis" />
  ),
  StickyXAxis: (props: any) => (
    <g data-testid="sticky-x-axis" className="x-axis" />
  ),
  ChartTooltip: ({ isVisible, content }: any) =>
    isVisible ? (
      <div
        className="chart-tooltip"
        data-testid="chart-tooltip"
        style={{ left: '100px', top: '200px', display: 'block' }}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    ) : (
      <div
        className="chart-tooltip"
        style={{ display: 'none' }}
      />
    ),
  ChartLegend: ({ valueHeaders, badgeColors, style }: any) => (
    <div className="vessel-bar-chart-legend" data-testid="chart-legend" style={style}>
      {valueHeaders.map((header: string, index: number) => (
        <div key={header} className="legend-item">
          <div
            className="legend-color"
            style={{ backgroundColor: badgeColors[index] }}
          />
          <span>{header}</span>
        </div>
      ))}
    </div>
  ),
}));

// Mock scale functions
const mockYScale = Object.assign(
  jest.fn((value) => value * 10),
  {
    bandwidth: () => 30,
    domain: () => ['item1', 'item2'],
  }
);

const mockXScale = Object.assign(
  jest.fn((value) => value * 5),
  {
    domain: () => [0, 100],
    range: () => [0, 500],
  }
);

// Import the mocked components
const {
  ScrollableChartElements,
  StickyXAxis,
  ChartTooltip,
  ChartLegend
} = require('../../DashboardWidget/common/ChartComponents');

describe('ChartComponents', () => {
  describe('ScrollableChartElements', () => {
    const props = {
      yScale: mockYScale,
      xScale: mockXScale,
      height: 200,
      margin: { top: 20, right: 20, bottom: 50, left: 150 },
    };

    test('renders without crashing', () => {
      const { getByTestId } = render(
        <svg>
          <ScrollableChartElements {...props} />
        </svg>
      );

      expect(getByTestId('scrollable-chart-elements')).toBeInTheDocument();
    });

    test('applies correct CSS classes', () => {
      const { container } = render(
        <svg>
          <ScrollableChartElements {...props} />
        </svg>
      );

      const element = container.querySelector('[data-testid="scrollable-chart-elements"]');
      expect(element).toHaveClass('x-grid');
      expect(element).toHaveClass('y-axis');
    });
  });

  describe('StickyXAxis', () => {
    const props = {
      xScale: mockXScale,
      width: 800,
      height: 50,
      margin: { left: 150 },
    };

    test('renders without crashing', () => {
      const { getByTestId } = render(
        <svg>
          <StickyXAxis {...props} />
        </svg>
      );

      expect(getByTestId('sticky-x-axis')).toBeInTheDocument();
    });

    test('applies correct CSS class', () => {
      const { container } = render(
        <svg>
          <StickyXAxis {...props} />
        </svg>
      );

      const element = container.querySelector('[data-testid="sticky-x-axis"]');
      expect(element).toHaveClass('x-axis');
    });
  });

  describe('ChartTooltip', () => {
    test('renders when visible', () => {
      const props = {
        content: '<div>Test tooltip content</div>',
        position: { x: 100, y: 200 },
        isVisible: true,
      };

      const { getByTestId, container } = render(<ChartTooltip {...props} />);

      const tooltip = getByTestId('chart-tooltip');
      expect(tooltip).toBeInTheDocument();
      expect(tooltip).toHaveStyle({
        left: '100px',
        top: '200px',
        display: 'block',
      });
    });

    test('hides when not visible', () => {
      const props = {
        content: '<div>Test tooltip content</div>',
        position: { x: 100, y: 200 },
        isVisible: false,
      };

      const { container } = render(<ChartTooltip {...props} />);

      const tooltip = container.querySelector('.chart-tooltip');
      expect(tooltip).toHaveStyle({ display: 'none' });
    });

    test('renders HTML content correctly', () => {
      const props = {
        content: '<div class="tooltip-content"><b>Test</b></div>',
        position: { x: 100, y: 200 },
        isVisible: true,
      };

      const { getByTestId } = render(<ChartTooltip {...props} />);

      const tooltip = getByTestId('chart-tooltip');
      expect(tooltip.innerHTML).toContain('<b>Test</b>');
    });
  });

  describe('ChartLegend', () => {
    const props = {
      valueHeaders: ['Status A', 'Status B', 'Status C'],
      badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
      style: { paddingLeft: '220px' },
    };

    test('renders without crashing', () => {
      const { getByTestId } = render(<ChartLegend {...props} />);

      expect(getByTestId('chart-legend')).toBeInTheDocument();
    });

    test('renders all legend items', () => {
      const { container } = render(<ChartLegend {...props} />);

      const legendItems = container.querySelectorAll('.legend-item');
      expect(legendItems).toHaveLength(3);
    });

    test('applies correct colors to legend items', () => {
      const { container } = render(<ChartLegend {...props} />);

      const colorElements = container.querySelectorAll('.legend-color');
      expect(colorElements[0]).toHaveStyle({ backgroundColor: '#ff0000' });
      expect(colorElements[1]).toHaveStyle({ backgroundColor: '#00ff00' });
      expect(colorElements[2]).toHaveStyle({ backgroundColor: '#0000ff' });
    });

    test('displays correct labels', () => {
      const { getByText } = render(<ChartLegend {...props} />);

      expect(getByText('Status A')).toBeInTheDocument();
      expect(getByText('Status B')).toBeInTheDocument();
      expect(getByText('Status C')).toBeInTheDocument();
    });

    test('applies custom styles', () => {
      const { getByTestId } = render(<ChartLegend {...props} />);

      const legend = getByTestId('chart-legend');
      expect(legend).toHaveStyle({ paddingLeft: '220px' });
    });

    test('handles empty arrays gracefully', () => {
      const emptyProps = {
        valueHeaders: [],
        badgeColors: [],
        style: {},
      };

      const { container } = render(<ChartLegend {...emptyProps} />);

      const legendItems = container.querySelectorAll('.legend-item');
      expect(legendItems).toHaveLength(0);
    });
  });
});
