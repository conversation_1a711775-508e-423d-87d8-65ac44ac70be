import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
  ScrollableChartElements, 
  StickyXAxis, 
  ChartTooltip, 
  ChartLegend 
} from '../../DashboardWidget/common/ChartComponents';

// Mock d3
jest.mock('d3', () => ({
  select: jest.fn(() => ({
    call: jest.fn().mockReturnThis(),
    selectAll: jest.fn(() => ({
      call: jest.fn().mockReturnThis(),
      attr: jest.fn().mockReturnThis(),
    })),
  })),
  axisLeft: jest.fn(() => ({
    tickFormat: jest.fn().mockReturnThis(),
  })),
  axisBottom: jest.fn(() => ({
    tickSize: jest.fn().mockReturnThis(),
    tickFormat: jest.fn().mockReturnThis(),
  })),
}));

// Mock scale functions
const mockYScale = Object.assign(
  jest.fn((value) => value * 10),
  {
    bandwidth: () => 30,
    domain: () => ['item1', 'item2'],
  }
);

const mockXScale = Object.assign(
  jest.fn((value) => value * 5),
  {
    domain: () => [0, 100],
    range: () => [0, 500],
  }
);

describe('ChartComponents', () => {
  describe('ScrollableChartElements', () => {
    const props = {
      yScale: mockYScale,
      xScale: mockXScale,
      height: 200,
      margin: { top: 20, right: 20, bottom: 50, left: 150 },
    };

    test('renders without crashing', () => {
      const { container } = render(
        <svg>
          <ScrollableChartElements {...props} />
        </svg>
      );
      
      expect(container.querySelector('.x-grid')).toBeInTheDocument();
      expect(container.querySelector('.y-axis')).toBeInTheDocument();
    });

    test('applies correct transforms', () => {
      const { container } = render(
        <svg>
          <ScrollableChartElements {...props} />
        </svg>
      );
      
      const xGrid = container.querySelector('.x-grid');
      const yAxis = container.querySelector('.y-axis');
      
      expect(xGrid).toHaveAttribute('transform', 'translate(150, 270)');
      expect(yAxis).toHaveAttribute('transform', 'translate(150, 0)');
    });
  });

  describe('StickyXAxis', () => {
    const props = {
      xScale: mockXScale,
      width: 800,
      height: 50,
      margin: { left: 150 },
    };

    test('renders without crashing', () => {
      const { container } = render(
        <svg>
          <StickyXAxis {...props} />
        </svg>
      );
      
      expect(container.querySelector('.x-axis')).toBeInTheDocument();
    });

    test('applies correct transform', () => {
      const { container } = render(
        <svg>
          <StickyXAxis {...props} />
        </svg>
      );
      
      const xAxis = container.querySelector('.x-axis');
      expect(xAxis).toHaveAttribute('transform', 'translate(150, 0)');
    });
  });

  describe('ChartTooltip', () => {
    test('renders when visible', () => {
      const props = {
        content: '<div>Test tooltip content</div>',
        position: { x: 100, y: 200 },
        isVisible: true,
      };

      const { container } = render(<ChartTooltip {...props} />);
      
      const tooltip = container.querySelector('.chart-tooltip');
      expect(tooltip).toBeInTheDocument();
      expect(tooltip).toHaveStyle({
        left: '100px',
        top: '200px',
        display: 'block',
      });
    });

    test('hides when not visible', () => {
      const props = {
        content: '<div>Test tooltip content</div>',
        position: { x: 100, y: 200 },
        isVisible: false,
      };

      const { container } = render(<ChartTooltip {...props} />);
      
      const tooltip = container.querySelector('.chart-tooltip');
      expect(tooltip).toHaveStyle({ display: 'none' });
    });

    test('renders HTML content correctly', () => {
      const props = {
        content: '<div class="tooltip-content"><b>Test</b></div>',
        position: { x: 100, y: 200 },
        isVisible: true,
      };

      const { container } = render(<ChartTooltip {...props} />);
      
      const tooltip = container.querySelector('.chart-tooltip');
      expect(tooltip?.innerHTML).toContain('<b>Test</b>');
    });
  });

  describe('ChartLegend', () => {
    const props = {
      valueHeaders: ['Status A', 'Status B', 'Status C'],
      badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
      style: { paddingLeft: '220px' },
    };

    test('renders without crashing', () => {
      const { container } = render(<ChartLegend {...props} />);
      
      expect(container.querySelector('.vessel-bar-chart-legend')).toBeInTheDocument();
    });

    test('renders all legend items', () => {
      const { container } = render(<ChartLegend {...props} />);
      
      const legendItems = container.querySelectorAll('.legend-item');
      expect(legendItems).toHaveLength(3);
    });

    test('applies correct colors to legend items', () => {
      const { container } = render(<ChartLegend {...props} />);
      
      const colorElements = container.querySelectorAll('.legend-color');
      expect(colorElements[0]).toHaveStyle({ backgroundColor: '#ff0000' });
      expect(colorElements[1]).toHaveStyle({ backgroundColor: '#00ff00' });
      expect(colorElements[2]).toHaveStyle({ backgroundColor: '#0000ff' });
    });

    test('displays correct labels', () => {
      const { getByText } = render(<ChartLegend {...props} />);
      
      expect(getByText('Status A')).toBeInTheDocument();
      expect(getByText('Status B')).toBeInTheDocument();
      expect(getByText('Status C')).toBeInTheDocument();
    });

    test('applies custom styles', () => {
      const { container } = render(<ChartLegend {...props} />);
      
      const legend = container.querySelector('.vessel-bar-chart-legend');
      expect(legend).toHaveStyle({ paddingLeft: '220px' });
    });

    test('handles empty arrays gracefully', () => {
      const emptyProps = {
        valueHeaders: [],
        badgeColors: [],
        style: {},
      };

      const { container } = render(<ChartLegend {...emptyProps} />);
      
      const legendItems = container.querySelectorAll('.legend-item');
      expect(legendItems).toHaveLength(0);
    });
  });
});
