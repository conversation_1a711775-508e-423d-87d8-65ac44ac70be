import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmptyState from '../../DashboardWidget/common/EmptyState';

describe('EmptyState Component', () => {
  test('renders without crashing with required text prop', () => {
    render(<EmptyState text="No data" />);
    expect(screen.getByText('No data')).toBeInTheDocument();
  });

  test('renders with custom text', () => {
    const customText = 'No data available';
    render(<EmptyState text={customText} />);

    expect(screen.getByText(customText)).toBeInTheDocument();
  });

  test('renders the SVG icon', () => {
    const { container } = render(<EmptyState text="No data" />);

    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '48');
    expect(svg).toHaveAttribute('height', '49');
  });

  test('has correct CSS class structure', () => {
    const { container } = render(<EmptyState text="No data" />);

    expect(container.querySelector('.empty-state-container')).toBeInTheDocument();
    expect(container.querySelector('.empty-state-text')).toBeInTheDocument();
  });

  test('displays text in correct element', () => {
    const testText = 'Test message';
    const { container } = render(<EmptyState text={testText} />);

    const textElement = container.querySelector('.empty-state-text');
    expect(textElement).toHaveTextContent(testText);
    expect(textElement?.tagName.toLowerCase()).toBe('p');
  });

  test('handles empty string text', () => {
    render(<EmptyState text="" />);

    const textElement = document.querySelector('.empty-state-text');
    expect(textElement).toBeInTheDocument();
    expect(textElement).toHaveTextContent('');
  });

  test('handles long text content', () => {
    const longText = 'This is a very long message that should still be displayed correctly in the empty state component';
    render(<EmptyState text={longText} />);

    expect(screen.getByText(longText)).toBeInTheDocument();
  });

  test('renders consistently across multiple instances', () => {
    const { container: container1 } = render(<EmptyState text="Test 1" />);
    const { container: container2 } = render(<EmptyState text="Test 2" />);

    // Both should have similar structure
    expect(container1.firstChild?.nodeName).toBe(container2.firstChild?.nodeName);
    expect(container1.querySelector('.empty-state-container')).toBeInTheDocument();
    expect(container2.querySelector('.empty-state-container')).toBeInTheDocument();
  });

  test('is accessible', () => {
    const text = 'No data available';
    render(<EmptyState text={text} />);

    // Check that text content is accessible
    expect(screen.getByText(text)).toBeInTheDocument();
  });

  test('maintains proper DOM structure', () => {
    const { container } = render(<EmptyState text="Test" />);

    const emptyStateContainer = container.querySelector('.empty-state-container');
    expect(emptyStateContainer?.children).toHaveLength(2); // SVG + p element

    const svg = emptyStateContainer?.querySelector('svg');
    const text = emptyStateContainer?.querySelector('p');

    expect(svg).toBeInTheDocument();
    expect(text).toBeInTheDocument();
  });
});
