import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmptyState from '../../DashboardWidget/common/EmptyState';

describe('EmptyState Component', () => {
  test('renders without crashing', () => {
    render(<EmptyState />);
    // Component should render without throwing an error
    expect(document.body).toBeInTheDocument();
  });

  test('renders with default message when no props provided', () => {
    const { container } = render(<EmptyState />);
    expect(container.firstChild).toBeInTheDocument();
  });

  test('renders custom message when provided', () => {
    const customMessage = 'No data available';
    render(<EmptyState message={customMessage} />);
    
    expect(screen.getByText(customMessage)).toBeInTheDocument();
  });

  test('renders custom title when provided', () => {
    const customTitle = 'Empty Results';
    render(<EmptyState title={customTitle} />);
    
    expect(screen.getByText(customTitle)).toBeInTheDocument();
  });

  test('renders both title and message when provided', () => {
    const customTitle = 'No Results Found';
    const customMessage = 'Try adjusting your search criteria';
    
    render(<EmptyState title={customTitle} message={customMessage} />);
    
    expect(screen.getByText(customTitle)).toBeInTheDocument();
    expect(screen.getByText(customMessage)).toBeInTheDocument();
  });

  test('renders icon when provided', () => {
    const TestIcon = () => <svg data-testid="test-icon">Test Icon</svg>;
    
    render(<EmptyState icon={<TestIcon />} />);
    
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  test('renders action button when provided', () => {
    const mockAction = jest.fn();
    const actionText = 'Retry';
    
    render(<EmptyState action={{ text: actionText, onClick: mockAction }} />);
    
    const button = screen.getByText(actionText);
    expect(button).toBeInTheDocument();
    expect(button.tagName.toLowerCase()).toBe('button');
  });

  test('calls action callback when button is clicked', () => {
    const mockAction = jest.fn();
    const actionText = 'Retry';
    
    render(<EmptyState action={{ text: actionText, onClick: mockAction }} />);
    
    const button = screen.getByText(actionText);
    button.click();
    
    expect(mockAction).toHaveBeenCalledTimes(1);
  });

  test('applies custom className when provided', () => {
    const customClass = 'custom-empty-state';
    const { container } = render(<EmptyState className={customClass} />);
    
    expect(container.firstChild).toHaveClass(customClass);
  });

  test('renders with all props combined', () => {
    const props = {
      title: 'No Vessels Found',
      message: 'There are no vessels matching your criteria',
      icon: <svg data-testid="vessel-icon">Vessel Icon</svg>,
      action: { text: 'Clear Filters', onClick: jest.fn() },
      className: 'vessel-empty-state',
    };
    
    render(<EmptyState {...props} />);
    
    expect(screen.getByText(props.title)).toBeInTheDocument();
    expect(screen.getByText(props.message)).toBeInTheDocument();
    expect(screen.getByTestId('vessel-icon')).toBeInTheDocument();
    expect(screen.getByText(props.action.text)).toBeInTheDocument();
  });

  test('handles empty string props gracefully', () => {
    render(<EmptyState title="" message="" />);
    
    // Should render without crashing even with empty strings
    expect(document.body).toBeInTheDocument();
  });

  test('is accessible', () => {
    render(<EmptyState title="No Data" message="Please try again later" />);
    
    // Check that text content is accessible
    expect(screen.getByText('No Data')).toBeInTheDocument();
    expect(screen.getByText('Please try again later')).toBeInTheDocument();
  });

  test('renders consistently across multiple instances', () => {
    const { container: container1 } = render(
      <EmptyState title="Test 1" message="Message 1" />
    );
    const { container: container2 } = render(
      <EmptyState title="Test 2" message="Message 2" />
    );
    
    // Both should have similar structure
    expect(container1.firstChild?.nodeName).toBe(container2.firstChild?.nodeName);
  });
});
