import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import StatCard from '../../StatCard/StatCard';

describe('StatCard', () => {
  const mockOnClick = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders title and count when not loading', () => {
    render(<StatCard title="Test Title" count={42} />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
  });

  it('renders subtitle when provided', () => {
    render(<StatCard title="Test" count={42} subTitle="Subtitle" />);
    expect(screen.getByText('Subtitle')).toBeInTheDocument();
  });

  it('renders loading skeletons when isLoading is true', () => {
    render(<StatCard isLoading={true} />);
    expect(document.querySelector('.skeleton-title')).toBeInTheDocument();
    expect(document.querySelector('.skeleton-subtitle')).toBeInTheDocument();
    expect(document.querySelector('.loading')).toBeInTheDocument();
  });

  it('renders clickable icon when onClick provided and not loading', () => {
    render(<StatCard title="Test" onClick={mockOnClick} />);
    const iconButton = screen.getByRole('button');
    expect(iconButton).toBeInTheDocument();
    
    fireEvent.click(iconButton);
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('does not render icon when loading', () => {
    render(<StatCard title="Test" onClick={mockOnClick} isLoading={true} />);
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('does not render subtitle section when no subtitle provided', () => {
    render(<StatCard title="Test" count={42} />);
    expect(screen.queryByText('action-card-description')).not.toBeInTheDocument();
  });
});