import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import YoutubeEmbed from '../YoutubeEmbed';

describe('YoutubeEmbed', () => {
  it('renders iframe with correct src', () => {
    render(<YoutubeEmbed embedId="dQw4w9WgXcQ" />);
    const iframe = screen.getByTitle('Embedded youtube');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', 'https://www.youtube.com/embed/dQw4w9WgXcQ');
  });

  it('renders with numeric embedId', () => {
    render(<YoutubeEmbed embedId={12345} />);
    const iframe = screen.getByTitle('Embedded youtube');
    expect(iframe).toHaveAttribute('src', 'https://www.youtube.com/embed/12345');
  });

  it('has responsive video class', () => {
    render(<YoutubeEmbed embedId="test" />);
    expect(screen.getByTitle('Embedded youtube').parentElement).toHaveClass('video-responsive');
  });
});
