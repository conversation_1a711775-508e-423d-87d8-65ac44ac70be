@import "../node_modules/bootstrap/scss/functions";
@import "../node_modules/bootstrap/scss/variables";

$font-family-base: Inter;

$dark: #343A40;

$theme-colors: (
    "primary": #1F4A70,
    "secondary": #0091B8,
    "success": #28A747,
    "info": #17A2B8,
    "warning": #FFC107,
    "danger": #D41B56,
    "dark": $dark,
    "grey": #6C757D,
    "light": #F8F9FA,
    "white": #FFFFFF,
    "background": #FAFAFA,
    "block": #EFEFEF,
    "body-text": #333333,
);

$body-bg: #FAFAFA;
$body-color:  $dark;

$grid-breakpoints: (
    "xs": 0,
    "sm": 576px,
    "md": 768px,
    "lg": 992px,
    "xl": 1440px,
    "xxl": 1600px
);

$container-max-widths: (
    "sm": 100%,
    "md": 100%,
    "lg": 100%,
    "xl": 100%,
    "xxl": 100%,
);

@font-face {
    font-family: "Inter";
    font-style:  normal;
    font-weight: 400;
    font-display: swap;
    src: url("fonts/Inter-Regular.woff") format("woff")
}

@font-face {
    font-family: "Inter";
    font-style:  normal;
    font-weight: 600;
    font-display: swap;
    src: url("fonts/Inter-SemiBold.woff") format("woff")
}

@font-face {
    font-family: "Inter";
    font-style:  normal;
    font-weight: 500;
    font-display: swap;
    src: url("fonts/Inter-Medium.woff") format("woff")
  }

  @import "../node_modules/bootstrap/scss/bootstrap.scss";

  .paris2-icon {
    display: inline-block;

    svg {
      display: inline-block;
      stroke: currentColor;
      fill: currentColor;
      width: 100%;
      height: 100%;
    }
  }